name: Release

# 限定推送 tag 形式为 x.y.z 的时候触发
on:
  push:
    tags:
      - '*.*.*'

env:
  registry: ghcr.io
  registry-username: ${{ github.actor }}
  registry-password: ${{ secrets.GITHUB_TOKEN }}
  # TODO: 确保此处的 secrets 在 GitHub 仓库中都存在且无误
  server-host: ${{ secrets.TENCENT_SERVER_HOST }}
  server-username: ${{ secrets.TENCENT_SERVER_USERNAME }}
  server-password: ${{ secrets.TENCENT_SERVER_PASSWORD }}
  node-env: production

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build-and-push-image:
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      # TODO: 修改此处的dockerfile路径，镜像名，容器名和端口号。可以根据需要添加多个镜像。
      matrix:
        include:
          - dockerfile: ./deploy/production/dockerfile
            image: ${{ github.repository }}/gp-fff-frontend
            container: gp-fff-frontend
            port: 4173:4173

    permissions:
      contents: write
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.registry }}
          username: ${{ env.registry-username }}
          password: ${{ env.registry-password }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.registry }}/${{ matrix.image }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        id: docker_build
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          build-args: GITHUB_TOKEN=${{ secrets.READ_PACKAGES_IN_GITHUB_ACTIONS_FOREVER }}

      - name: Deploy to Server
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ env.server-host }}
          username: ${{ env.server-username }}
          password: ${{ env.server-password }}
          script: |
            echo "开始登录"
            docker login -u ${{ env.registry-username }} -p ${{ env.registry-password }} ${{ env.registry }}

            echo "开始获取当前运行信息"
            container_name="${{ matrix.container }}"
            image_id=$(docker inspect --format='{{.Image}}' $container_name 2>/dev/null)
            if [ -z "$image_id" ]; then
                echo "容器 $container_name 不存在"
            else
                image_tag=$(docker image inspect --format='{{index .RepoTags 0}}' $image_id | cut -d ":" -f 2)
                echo "容器 $container_name 的当前镜像标签版本号为: $image_tag"
            fi

            echo "开始拉取镜像，新版本号为: ${{ github.ref_name }}"
            docker pull ${{ env.registry }}/${{ matrix.image }}:${{ github.ref_name }}
            echo "开始停止并删除旧容器"
            docker stop ${{ matrix.container }} || true && docker rm ${{ matrix.container }} || true
            echo "开始删除旧镜像"
            docker rmi ${{ env.registry }}/${{ matrix.image }}:${image_tag} || true
            echo "开始启动新容器"
            docker run --restart=always --env NODE_ENV=${{ env.node-env }} -d -p ${{ matrix.port }} --name ${{ matrix.container }} ${{ env.registry }}/${{ matrix.image }}:${{ github.ref_name }}
