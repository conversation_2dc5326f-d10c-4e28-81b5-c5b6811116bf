import * as SelectPrimitive from '@radix-ui/react-select'

import { ChevronDownIcon } from './icons/chevron-down-icon'
import { Select, SelectContent, SelectItem, SelectValue } from './ui/select'

interface SelectWithIconProps {
  value: string
  onValueChange: (value: string) => void
  data: Array<{
    label: string
    value: string
  }>
  icon: React.ReactNode
  placeholder?: string
}

export function SelectWithIcon({ value, onValueChange, data, icon, placeholder }: SelectWithIconProps) {
  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectPrimitive.Trigger
        data-slot="select-trigger"
        className={"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex items-center justify-between gap-2 rounded-none border px-3 py-2 whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 w-[614px] !h-14 bg-white overflow-hidden p-0 relative before:content-[''] before:absolute before:right-0 before:top-0 before:w-14 before:h-full before:bg-[var(--color-blue)] before:z-0 [&>svg]:absolute [&>svg]:right-4 [&>svg]:top-1/2 [&>svg]:-translate-y-1/2 [&>svg]:z-10 [&>svg]:text-white [&>svg]:opacity-100 text-xl "}
      >
        <div className="flex items-center flex-1 pl-4 pr-14 relative z-10">
          {icon}
          <SelectValue placeholder={placeholder} className="text-gray-800 font-medium !text-xl flex-1 text-left" />
        </div>
        <SelectPrimitive.Icon asChild>
          <ChevronDownIcon className="text-white opacity-100 w-5 h-5 z-10 size-4" />
        </SelectPrimitive.Icon>
      </SelectPrimitive.Trigger>
      <SelectContent className="border border-gray-200 shadow-lg">
        {data?.map(({ label, value }) => (
          <SelectItem key={value} value={value} className="text-xl py-2 px-4 hover:bg-gray-50">
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
