import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const SelectIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 18.582763671875 23.064208984375"
      fill="none"
      className={className}
      {...props}
    >
      <path d="M18.3247 23.0418C18.3247 23.0925 17.0342 23.0418 17.0342 23.0418L9.29138 15.4796L1.54858 23.0418C1.54858 23.0418 0.258088 23.0925 0.258088 23.0418C0.103246 22.8387 0 22.4327 0 22.1789L0 6.14108C0 5.53205 0.516202 5.02451 1.13562 5.02451L17.4471 5.02451C18.0666 5.02451 18.5828 5.53205 18.5828 6.14108L18.5828 22.1789C18.5828 22.4327 18.4795 22.8387 18.3247 23.0418ZM17.4471 3.90796L1.13562 3.90796C0.516202 3.90796 0 3.40042 0 2.79139L0 1.11656C0 0.507529 0.516202 0 1.13562 0L17.4471 0C18.0666 0 18.5828 0.507529 18.5828 1.11656L18.5828 2.79139C18.5828 3.40042 18.0666 3.90796 17.4471 3.90796Z" fill="#081D3B" />
    </svg>
  )
}

export { SelectIcon }
