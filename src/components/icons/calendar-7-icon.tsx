import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const Calendar7Icon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20.2601318359375 21.32666015625"
      fill="none"
      className={className}
      {...props}
    >
      <path d="M0 20.2601C0 20.7308 0.578958 21.3266 1.06633 21.3266L19.1938 21.3266C19.6811 21.3266 20.2601 20.7308 20.2601 20.2601L20.2601 6.39849L0 6.39849L0 20.2601ZM7.46426 9.59746L13.8622 9.59746L13.8622 11.7296L10.6632 19.1933L8.53057 19.1933L11.7295 11.7296L7.46426 11.7296L7.46426 9.59746ZM19.1938 2.13318L15.9948 2.13318L15.9948 0L13.8622 0L13.8622 2.13318L6.39793 2.13318L6.39793 0L4.2653 0L4.2653 2.13318L1.06633 2.13318C0.578958 2.13318 0 2.72878 0 3.19893L0 5.33166L20.2601 5.33166L20.2601 3.41192C20.2601 2.94229 19.981 2.13318 19.1938 2.13318Z" fill="#081D3B" />
    </svg>
  )
}

export { Calendar7Icon }
