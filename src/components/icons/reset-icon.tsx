import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const ResetIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 37.89471435546875 37.89468765258789"
      fill="none"
      className={className}
      {...props}
    >
      <path d="M32.2527 32.4376C24.8018 39.7864 12.8068 39.7018 5.45746 32.2516C-1.89187 24.8014 -1.80725 12.8016 5.64363 5.45705C13.0945 -1.89169 25.0895 -1.80712 32.4389 5.64309C39.784 13.0891 39.7036 25.0889 32.2527 32.4376ZM18.9503 9.00034C17.4102 9.00034 15.9589 9.35974 14.66 9.98553L15.3031 8.45912L13.4626 7.68535L11.144 13.199L11.3767 13.2963L11.3259 13.4612L12.9041 13.939L12.9845 13.9728L12.9887 13.9643L13.6742 14.1715L13.6742 14.3745C13.7207 14.3195 13.7757 14.2688 13.8265 14.218L17.0548 15.199L17.6344 13.292L15.7897 12.7339C16.7374 12.2519 17.8037 11.9686 18.9418 11.9686C22.8005 11.9686 25.9273 15.0975 25.9273 18.9537C25.9273 22.8099 22.8005 25.9388 18.9418 25.9388C15.4258 25.9388 12.5275 23.3384 12.0367 19.96L9.04962 19.96C9.55311 24.9832 13.7926 28.907 18.9503 28.907C24.4464 28.907 28.9017 24.4504 28.9017 18.9537C28.9017 13.4527 24.4464 9.00034 18.9503 9.00034Z" fill="#001E3D" />
    </svg>
  )
}

export { ResetIcon }
