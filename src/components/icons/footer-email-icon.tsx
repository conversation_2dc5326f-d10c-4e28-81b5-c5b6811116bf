import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const FooterEmailIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 38.37890625 23.712890625"
      fill="none"
      className={className}
      {...props}
    >
      <rect x="2.321533203125" y="1.712890625" width="33.97184753417969" height="22" fill="#66CC00" />
      <path d="M19.1514 12.8187L1.16048 0.001176L0 1.63006L18.57 14.86L19.149 15.2725L19.7288 14.8612L38.3788 1.63124L37.2216 0L19.1514 12.8187Z" fillRule="evenodd" fill="#333333" />
    </svg>
  )
}

export { FooterEmailIcon }
