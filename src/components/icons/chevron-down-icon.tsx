import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const ChevronDownIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 26 26"
      fill="none"
      className={className}
      {...props}
    >
      <g opacity="0">
        <rect x="0" y="0" width="26" height="26" fill="#CCCCCC" />
      </g>
      <path fill="#FFFFFF" d="M12.4702 19.0103L4.55067 8.44915C4.25067 8.04909 4.53613 7.47823 5.03618 7.47823L20.8753 7.47823C21.3753 7.47823 21.6608 8.04909 21.3608 8.44915L13.4412 19.0103C13.1985 19.334 12.713 19.334 12.4702 19.0103Z" />
    </svg>
  )
}

export { ChevronDownIcon }
