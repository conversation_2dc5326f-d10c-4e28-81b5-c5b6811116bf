import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const FooterLogoIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 707 107"
      fill="none"
      className={className}
      {...props}
    >
      <rect x="0" y="0" width="707" height="107" fill="#66CC00" />
      <path d="M435.056 43.7564C436.171 45.6092 431.674 48.8844 430.187 49.8646C427.548 51.5978 424.673 51.6576 425.528 47.976C427.374 47.0317 428.737 42.7882 430.249 42.1547C431.302 41.7005 434.622 43.0392 435.068 43.7684L435.056 43.7564Z" fill="#000000" />
      <path d="M423.954 52.483L424.871 52.9492L434.833 51.467C436.518 51.7778 438.649 54.5509 438.327 56.2842C438.042 57.4437 426.023 56.8699 423.954 57.4197C423.905 59.918 424.239 68.9786 422.492 70.6402C421.687 71.4052 419.655 70.8553 418.837 70.2816C416.669 68.7635 419.853 60.8504 418.143 58.4716C417.251 57.8501 410.102 61.197 408.454 59.6072C405.58 55.2921 406.175 58.8661 403.56 60.8623C402.842 61.4122 400.81 62.4641 399.93 62.5716C397.7 62.8466 396.04 60.946 393.314 62.9542L391.455 60.0136L389.225 62.1772L385.855 60.432C385.087 64.018 386.772 70.5804 380.726 68.6439C379.834 68.0463 382.113 63.0737 380.911 61.723C378.929 63.3008 376.91 65.6795 374.06 64.2093C371.631 62.9542 374.332 61.9501 374.89 61.3165C375.497 60.6352 380.49 54.8259 380.019 54.4075C376.203 55.6028 370.801 58.9737 370.603 52.6862L368.757 57.1448C367.295 59.3681 362.017 59.4877 359.316 60.1332C357.68 60.5157 356.751 61.8544 354.471 60.8623C354.868 66.827 365.028 63.1932 367.691 60.4559C369.661 61.3046 369.959 65.046 368.695 66.3489C368.261 66.7912 363.925 68.381 363.095 68.5005C354.409 69.7915 347.161 65.8349 351.312 56.7982L350.606 56.2125C348.363 56.9775 346.765 55.041 346.802 52.9612C351.684 51.1801 356.726 48.9807 360.282 45.1675C354.694 43.6136 354.806 52.9372 349.59 48.12L356.974 35.9873C360.344 37.0512 360.964 38.9757 359.378 41.9401C361.967 41.1392 365.028 39.1789 365.573 43.1354C365.907 45.5978 362.996 46.841 362.971 47.3191C362.934 48.5145 366.923 46.7932 367.766 46.8051C370.368 46.829 371.631 50.2238 370.591 52.2679L381.605 48.8611L382.25 45.1914L381.345 44.7252C379.995 46.2553 376.092 44.8567 376.414 43.0518C378.706 42.3704 384.034 37.1827 385.731 36.8719C387.429 36.5611 390.167 38.39 390.799 40.0395C388.519 42.8008 385.694 41.9043 386.735 46.6975C389.56 45.2751 390.266 47.6897 391.047 47.9168C391.827 48.1439 392.781 47.8451 393.586 48.0722C394.206 48.2515 394.553 49.0763 395.073 49.1839C395.941 49.3632 398.369 48.1679 399.534 48.0244C402.916 47.618 405.964 48.6101 407.092 51.8734C407.699 53.6425 406.584 54.6824 407.562 55.7104C410.883 53.9413 415.665 55.1725 419.035 53.4034C419.593 52.734 419.692 43.4223 419.246 42.8008C418.366 41.5935 414.315 43.4223 412.295 42.1553C411.713 41.7847 409.954 39.2984 410.908 38.7366C411.577 38.3422 416.892 38.2585 418.267 38.0553C420.535 37.7206 428.464 35.6407 429.877 36.0351C430.744 36.2862 432.9 38.8322 432.243 39.8363L423.669 42.3943C423.471 42.6932 423.93 42.9561 423.93 43.0518L423.93 52.5069L423.954 52.483ZM390.786 50.1162C387.478 49.9728 386.685 51.718 386.351 54.5629L391.678 57.4197L390.786 50.1162ZM366.576 50.9769L362.748 52.2439L362.091 55.2681C365.251 55.2801 366.403 53.9174 366.576 50.9769ZM396.672 57.7903C398.555 58.9139 403.746 55.1367 402.024 52.8775C400.463 50.8335 398.183 53.3915 396.188 53.1404L396.672 57.7903ZM358.51 53.5588C355.896 53.3795 355.401 54.3597 355.215 56.6547C355.165 57.2046 354.769 57.3361 355.611 57.8501C357.817 57.3958 358.795 55.6148 358.51 53.5588Z" fill="#000000" />
      <path d="M342.825 51.8375L344.782 53.5109C346.294 56.1287 339.888 57.4316 340.161 58.878C340.384 60.0972 345.885 62.1771 347.112 62.5596C348.388 64.3407 344.535 66.3727 342.726 65.9305C341.536 65.6436 337.77 61.7826 337.46 62.1532C338.34 65.8348 333.78 71.429 331.253 66.5879C331.178 65.6675 335.403 62.9063 333.198 61.3045C330.373 64.508 326.47 68.7395 324.004 62.5955L331.178 59.3681C328.836 57.7663 325.95 58.6867 326.681 54.8736C325.838 54.8497 324.24 54.7421 323.781 54.001C320.622 48.9567 336.481 51.4669 336.109 47.1159C332.876 47.9646 329.183 49.3392 329.852 44.5579L337.906 43.243L337.906 41.5337C337.064 40.6969 325.392 45.6815 328.465 39.143C332.628 38.9518 337.287 36.7762 341.301 37.6369C344.659 40.3981 341.623 44.1873 340.595 47.3191C342.899 48.5981 344.857 47.1876 345.513 50.7377L342.837 51.8494L342.825 51.8375ZM333.854 60.8622L334.561 53.5468C333.681 52.3514 330.311 54.2998 328.936 53.9891L332.776 56.559L332.07 58.7106L333.371 58.7345L333.854 60.8622Z" fill="#000000" />
      <path d="M322.208 58.2807C320.064 58.1971 317.648 60.8627 315.269 60.4443C314.241 60.265 313.262 58.6393 313.213 57.6472C313.126 55.6869 317.363 52.6507 317.735 50.7621C317.983 49.507 315.245 50.5708 314.13 49.4831C311.875 47.2717 316.756 43.9367 317.921 42.3469C319.271 40.5061 320.337 34.9239 323.422 37.3504C327.461 40.53 318.763 45.7656 319.965 47.1043C326.854 48.5029 320.399 52.567 319.073 55.6988L321.787 55.8901C322.332 56.2606 322.295 57.6592 322.208 58.2807C322.518 58.2807 322.815 58.3644 323.1 58.5079C325.801 60.6834 319.841 69.5289 315.48 67.5207C314.563 67.1023 313.77 65.2137 314.353 64.3052C315.158 63.0621 321.861 60.5399 322.208 58.2807Z" fill="#000000" />
      <path d="M416.719 51.7774L413.87 53.1401C411.9 53.152 407.266 45.1313 412.544 44.9401C415.443 44.8325 416.732 49.5062 416.719 51.7893L416.719 51.7774Z" fill="#000000" />
      <path d="M87.3552 66.6588C86.7357 68.7626 83.0807 67.5195 82.0399 65.7384C79.3884 61.232 84.0471 57.9329 80.4168 54.8489C79.661 54.8489 77.3813 61.3874 77.1582 62.4632C76.6626 64.9256 77.0963 69.3603 76.514 71.2968C76.0184 72.9583 73.4784 73.2451 71.9916 72.8029C68.9313 71.9064 71.1863 64.8658 69.6747 64.7344C64.8178 66.9577 59.4034 66.9457 54.4102 65.1647C48.9091 63.2163 49.0702 60.5387 47.001 55.8769C46.9515 55.2673 48.7108 51.8845 49.1693 51.0956C51.7216 46.7445 61.1875 36.3451 66.255 35.2574C70.3437 34.3728 73.6519 37.4568 73.9121 41.3177C74.1103 44.4256 73.0819 44.8559 69.9349 44.4734C68.6463 36.9667 58.8706 47.0075 56.69 49.7209C54.9678 51.8725 50.9906 56.9647 54.0262 59.0685C54.6085 59.4749 59.4653 61.1244 60.2583 61.2559C62.6 61.6623 71.4836 60.4551 70.765 56.9886L59.5644 58.4947C57.4458 56.5344 57.4334 52.6854 60.4441 51.5976C62.6867 50.7848 73.7138 48.9918 75.9192 49.2309C79.438 49.6134 78.2981 52.0638 80.169 53.9644C80.4664 52.9125 80.169 51.4781 80.8628 50.5457C81.7673 49.3265 83.5515 49.8405 84.109 47.2107C84.7037 44.4017 83.6258 44.5212 83.0683 42.8238C80.5283 34.9824 92.3608 32.7591 97.2176 38.7238C101.728 44.2702 99.8691 49.5895 96.3751 55.0043L103.487 60.4311C104.354 58.172 103.673 55.7693 104.045 53.4384C104.28 51.9443 106.188 48.9559 106.176 48.167C106.151 47.1988 104.292 46.4218 104.02 45.0711C103.698 43.541 104.404 41.3297 105.779 40.481C108.084 39.0585 116.088 37.3731 119.086 36.9189C120.61 36.6798 123.274 36.2256 124.538 37.0384C125.715 37.7915 127.4 41.5329 126.594 42.8238C125.715 44.2224 115.084 44.6527 112.668 46.6608C111.825 47.3661 111.602 48.8364 112.755 49.195C116.063 50.2469 121.638 45.2743 123.373 50.6174C125.69 57.7536 114.811 54.562 110.537 56.6539C108.121 57.8373 111.008 59.7379 112.804 59.9291C115.406 60.204 118.59 56.7854 119.879 61.2798C121.911 68.404 114.526 68.0813 109.657 66.36C106.572 65.2603 105.259 63.5988 103.722 60.8734C100.836 73.293 93.6617 61.2201 88.2597 58.0763L87.3676 58.5186L87.3676 66.6828L87.3552 66.6588ZM89.1517 50.1154C93.1413 49.6373 96.338 42.9194 90.7625 42.4055L90.1182 43.0987L89.1394 50.1154L89.1517 50.1154Z" fill="#000000" />
      <path d="M204.775 49.6736C205.977 51.2036 214.142 47.6057 216.199 47.9404C218.107 48.2631 219.606 53.6541 216.397 54.9928C213.659 56.1404 207.91 56.0686 204.676 56.6663C203.14 56.9532 200.141 57.4791 201.616 59.3797C203.957 62.392 209.744 58.1127 212.085 58.7701C215.715 61.9138 213.609 66.8267 208.938 67.6275C199.732 69.2054 194.182 64.4599 195.408 55.5188C195.817 52.5663 198.06 50.0442 198.506 47.1036C197.639 47.522 197.477 48.6695 196.672 49.4226C193.798 52.0882 189.523 53.2357 185.918 54.6821L184.406 66.8864C175.064 70.066 179.846 60.0252 178.707 56.4272C178.273 55.0526 175.733 55.3873 175.609 52.0523C175.436 46.9363 179.115 48.2392 179.623 47.4742L180.094 43.936C177.17 41.6529 174.407 46.1115 170.715 44.1034C170.666 47.6176 170.777 51.1319 170.727 54.6462C170.715 55.7698 170.244 56.8456 170.219 58.0649C170.17 61.1249 172.536 68.8946 166.019 67.7351C163.157 67.2211 163.046 62.3681 161.906 60.0611C161.41 59.0689 158.201 54.3474 157.421 54.0485C157.123 53.929 156.38 53.7975 156.33 54.2398C155.228 56.6783 155.946 64.5316 153.939 66.0617C151.288 68.0698 148.178 63.5993 148.326 60.7185C148.599 55.7937 150.705 50.2593 150.941 44.701C151.015 42.8961 149.64 36.9075 152.638 36.0229C159.254 34.0745 157.52 41.0194 158.412 44.0675C158.771 45.3107 161.051 48.0958 162.005 49.2074C162.612 49.9127 163.43 51.4427 164.421 51.4068L165.338 37.8757C166.577 34.7678 169.811 36.8118 170.938 38.9395C173.106 37.2302 175.064 36.9075 177.728 36.3935C181.135 35.736 188.755 35.4491 192.336 36.0349C197.874 36.9314 198.952 42.0474 199.162 46.6853L203.437 38.7363C204.874 38.2223 206.572 38.306 208.009 37.9594C211.181 37.2063 216 35.5089 218.924 35.0905C221.093 34.7798 221.464 35.1384 222.431 36.8357C223.335 38.4255 224.252 41.5095 222.518 42.8363C220.461 44.3903 210.524 44.8086 207.018 46.4462C205.37 47.2112 205.432 48.1914 204.788 49.6736L204.775 49.6736ZM185.063 41.9518C184.864 46.9721 185.645 48.4424 190.304 45.478C194.962 42.5136 187.578 41.641 185.063 41.9518Z" fill="#000000" />
      <path d="M239.74 64.9386C239.133 68.3573 233.966 67.4847 233.483 64.9386C233.186 63.3966 234.301 58.3882 232.194 58.0893C231.079 57.934 226.619 58.5675 225.603 59.1293C222.592 60.8147 223.038 69.2538 218.07 65.5243C213.733 62.273 218.664 58.4719 219.718 55.1966C220.089 54.0491 219.817 52.7701 220.25 51.8377C220.919 50.3914 227.015 41.5459 228.341 39.8246C229.382 38.4739 231.773 35.2943 233.594 35.5692C238.538 37.9002 239.083 44.1398 239.752 48.8255C240.805 49.0407 240.471 48.694 240.756 48.2756C242.453 45.7535 242.528 44.8331 245.427 42.8847C252.08 38.4261 265.684 34.4934 264.421 46.913C263.652 54.4555 254.335 51.3596 256.739 45.0004C253.976 43.829 249.652 47.1521 248.066 49.2797C247.669 49.8176 246.158 52.531 246.083 53.0092C245.712 55.4477 253.456 58.5675 255.661 58.7229C259.502 58.9978 260.617 56.2246 263.937 57.42C263.789 52.9494 266.465 49.2319 267.766 45.383C268.1 44.4028 267.592 42.9086 268.522 41.3905C269.922 39.0955 273.986 39.2867 276.216 38.8803C282.2 37.7926 288.197 36.8244 294.206 35.892C296.944 36.1669 298.567 40.5777 297.936 42.9564L297.266 43.6019C294.912 43.841 292.509 43.4943 290.167 43.6617C286.562 43.9246 278.57 44.343 275.596 45.5862C274.271 46.136 272.957 48.2159 274.234 49.0526C276.773 50.7141 284.914 44.7494 286.277 51.7182C287.293 56.9179 281.197 55.6509 278.062 55.9377C275.745 56.141 273.465 56.6908 271.111 56.5593C271 59.1173 274.655 59.2847 276.662 59.0934C279.313 58.8424 283.848 56.0931 284.146 60.2051C284.307 62.5838 283.588 64.7713 280.998 65.4048C276.699 66.4447 272.338 67.1619 268.274 65.0821C266.589 64.2214 266.056 63.0619 264.619 62.1535C262.252 66.8751 259.75 67.664 254.509 66.6838C248.19 65.5004 243.729 61.5319 239.74 56.9896C239.442 59.4162 240.149 62.6436 239.74 64.9386ZM233.47 44.1039C231.29 43.1835 229.072 50.1882 229.444 50.5468C230.608 50.5229 232.64 50.9173 233.26 49.6861L233.483 44.1039L233.47 44.1039Z" fill="#000000" />
      <path d="M149.107 43.1476C146.48 44.2831 138.253 43.6735 136.395 44.9286C135.875 45.2872 133.917 48.3712 134.413 48.8254C138.613 49.8055 143.371 43.8767 145.514 49.1362C148.45 56.344 138.514 54.8738 133.929 55.9018C126.446 57.5872 132.666 60.5875 137.733 59.1292C139.455 58.6391 140.372 56.8102 141.921 59.4639C145.105 64.9265 140.36 67.5563 135.082 67.1977C118.219 66.0502 126.88 49.686 129.655 40.207C130.559 39.1073 137.399 37.6132 139.208 37.3382C144.659 36.4895 151.263 35.6767 149.095 43.1715L149.107 43.1476Z" fill="#000000" />
      <rect x="464.18994140625" y="36.889892578125" width="1.9137420654296875" height="31.35009765625" fill="#000000" />
      <g clipPath="url(#clip-path-131_200)">
        <rect x="577.952392578125" y="35.721282958984375" width="7.053993225097656" height="30.631139397621155" fill="#000000" />
        <path d="M614.087 58.2483C614.087 58.2483 612.807 60.6223 610.809 60.6223C609.794 60.6223 609.096 60.0096 608.655 59.4076C608.25 58.8531 608.045 58.1744 608.045 57.4826L608.045 44.4327L600.991 44.4327L600.991 59.9199C600.991 60.5483 601.079 61.1741 601.256 61.7762C601.497 62.5948 601.925 63.7065 602.626 64.5541C603.879 66.0698 605.797 67.3664 608.045 67.2924C610.29 67.2159 612.776 65.7847 614.087 64.2425L614.087 66.355L621.141 66.355L621.141 44.43L614.087 44.43L614.087 58.2483Z" fill="#000000" />
        <rect x="511.6298828125" y="34.989837646484375" width="7.03582763671875" height="7.1587138175964355" fill="#000000" />
        <path d="M526.898 44.2556C526.161 44.4431 525.419 44.4616 522.839 45.8083C522.839 45.8083 521.432 46.5107 521.515 47.3557C521.593 48.1743 522.59 49.896 523.039 50.638C523.488 51.38 523.939 51.4672 525.149 50.6697C526.358 49.8722 528.04 49.051 529.999 49.3441C531.959 49.6372 532.826 50.5984 532.029 51.4038C531.567 52.2778 525.691 52.0428 522.579 55.0346C519.468 58.0264 520.244 61.5965 522.154 64.4299C523.836 66.9253 529.322 67.7597 533.76 64.1236L533.76 66.3496L539.521 66.3496L539.521 51.9504C539.521 51.9504 539.418 49.0114 537.85 46.5926C536.023 43.7724 530.482 43.3473 526.901 44.2556L526.898 44.2556ZM532.444 57.7624C532.444 58.9665 532.205 60.0518 531.375 60.7621C530.479 61.5279 528.824 61.9583 527.555 60.9047C525.865 59.5369 526.618 56.3391 532.444 55.8638L532.444 57.765L532.444 57.7624Z" fill="#000000" />
        <path d="M562.093 44.2556C561.356 44.4431 560.613 44.4616 558.034 45.8083C558.034 45.8083 556.63 46.5107 556.71 47.3557C556.788 48.1743 557.784 49.896 558.233 50.638C558.682 51.38 559.134 51.4672 560.343 50.6697C561.553 49.8722 563.235 49.051 565.194 49.3441C567.153 49.6372 568.02 50.5984 567.224 51.4038C566.762 52.2778 560.886 52.0428 557.774 55.0346C554.662 58.0264 555.438 61.5965 557.348 64.4299C559.03 66.9253 564.517 67.7597 568.955 64.1236L568.955 66.3496L574.716 66.3496L574.716 51.9504C574.716 51.9504 574.612 49.0114 573.045 46.5926C571.218 43.7724 565.677 43.3473 562.095 44.2556L562.093 44.2556ZM567.636 57.7624C567.636 58.9665 567.397 60.0518 566.567 60.7621C565.672 61.5279 564.016 61.9583 562.747 60.9047C561.057 59.5369 561.81 56.3391 567.636 55.8638L567.636 57.765L567.636 57.7624Z" fill="#000000" />
        <path d="M550.069 38.5704L543.015 38.5704L543.015 44.2556L539.519 44.2556L539.519 50.1204L543.015 50.1204L543.015 60.1363C543.189 62.3755 544.276 63.9044 545.42 64.9079C546.666 66.0011 548.268 66.5794 549.91 66.5794L554.805 66.5794L554.805 61.1107L551.859 61.1107C550.497 61.1107 550.066 60.1389 550.066 60.1389L550.066 50.1231L554.802 50.1231L554.802 44.2583L550.066 44.2583L550.066 38.5704L550.069 38.5704Z" fill="#000000" />
        <path d="M596.467 46.2309C595.214 44.7152 593.296 43.4187 591.048 43.4926C588.803 43.5692 586.317 45.0004 585.006 46.5425L585.006 52.5314C585.006 52.5314 586.286 50.1575 588.284 50.1575C589.299 50.1575 589.997 50.7701 590.438 51.3722C590.843 51.9267 591.051 52.6054 591.051 53.2972L591.051 66.3471L598.105 66.3471L598.105 50.8599C598.105 50.2314 598.017 49.6056 597.84 49.0036C597.599 48.185 597.171 47.0733 596.47 46.2256L596.467 46.2309Z" fill="#000000" />
        <path d="M636.801 43.5824C634.903 43.5824 633.139 44.3244 631.675 45.5945L631.675 35.7213L624.621 35.7213L624.621 66.3524L631.675 66.3524L631.675 65.2829C633.139 66.5531 634.903 67.2951 636.801 67.2951C641.88 67.2951 645.998 61.9874 645.998 55.4414C645.998 48.8953 641.88 43.5877 636.801 43.5877L636.801 43.5824ZM634.371 61.5306C633.354 61.5306 632.417 61.05 631.675 60.2499L631.675 50.7464C632.417 49.9436 633.354 49.4657 634.371 49.4657C636.79 49.4657 638.75 52.167 638.75 55.4995C638.75 58.8319 636.79 61.5333 634.371 61.5333L634.371 61.5306Z" fill="#000000" />
        <path d="M517.054 44.7257L509.6 44.7257L509.6 44.8207C509.6 44.9976 509.655 45.1719 509.753 45.3198C510.35 46.2229 511.3 48.1822 511.17 51.3879C511.181 57.4323 507.716 59.1751 507.716 59.1751L502.821 59.1751L502.821 44.7283L509.294 44.7283L509.294 37.5511L495.77 37.5511L495.77 66.3523L507.719 66.3523C507.719 66.3523 516.465 64.8419 518.136 57.0996C519.115 52.8429 518.725 48.4172 517.054 44.7309L517.054 44.7257Z" fill="#000000" />
      </g>
      <defs>
        <clipPath id="clip-path-131_200">
          <path d="M495.77 67.2895L645.998 67.2895L645.998 34.9895L495.77 34.9895L495.77 67.2895Z" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export { FooterLogoIcon }
