import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const LogoIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 249.000732421875 24.177734375"
      fill="none"
      className={className}
      {...props}
    >

      <path d="M246.899 5.57833C247.608 6.75715 244.746 8.84098 243.801 9.46461C242.121 10.5674 240.293 10.6054 240.836 8.26299C242.011 7.66217 242.878 4.96231 243.84 4.55923C244.51 4.27023 246.623 5.12202 246.906 5.58594L246.899 5.57833Z" fill="#FFFFFF" />
      <path d="M239.835 11.1324L240.419 11.429L246.757 10.4859C247.829 10.6837 249.185 12.4481 248.98 13.5508C248.798 14.2885 241.152 13.9235 239.835 14.2733C239.804 15.8628 240.017 21.6276 238.905 22.6847C238.393 23.1715 237.1 22.8216 236.58 22.4566C235.2 21.4907 237.226 16.456 236.138 14.9426C235.571 14.5471 231.022 16.6766 229.974 15.6651C228.145 12.9196 228.523 15.1936 226.86 16.4636C226.403 16.8135 225.11 17.4827 224.55 17.5512C223.131 17.7261 222.075 16.5169 220.34 17.7946L219.158 15.9237L217.739 17.3002L215.595 16.1898C215.106 18.4714 216.178 22.6467 212.331 21.4146C211.764 21.0344 213.214 17.8706 212.45 17.0112C211.188 18.0151 209.903 19.5285 208.09 18.5931C206.545 17.7946 208.264 17.1557 208.618 16.7526C209.005 16.3191 212.181 12.623 211.882 12.3568C209.454 13.1173 206.017 15.262 205.891 11.2617L204.716 14.0984C203.786 15.513 200.428 15.589 198.709 15.9997C197.669 16.2431 197.078 17.0949 195.627 16.4636C195.879 20.2586 202.343 17.9467 204.038 16.2051C205.292 16.745 205.481 19.1255 204.677 19.9544C204.401 20.2358 201.642 21.2473 201.114 21.3234C195.588 22.1447 190.976 19.6274 193.617 13.8779L193.168 13.5052C191.741 13.9919 190.724 12.7599 190.747 11.4366C193.853 10.3034 197.062 8.90403 199.324 6.47792C195.769 5.48924 195.84 11.4214 192.521 8.35645L197.219 0.637093C199.364 1.31396 199.758 2.53841 198.749 4.42451C200.396 3.91496 202.343 2.6677 202.69 5.18503C202.903 6.75171 201.051 7.54266 201.035 7.84687C201.011 8.60742 203.55 7.51224 204.086 7.51984C205.741 7.53505 206.545 9.69497 205.883 10.9955L212.891 8.82797L213.301 6.49313L212.725 6.19653C211.866 7.17 209.383 6.28019 209.588 5.13179C211.046 4.6983 214.436 1.39762 215.516 1.19988C216.596 1.00215 218.338 2.16575 218.74 3.21527C217.29 4.97208 215.492 4.40169 216.155 7.45139C217.952 6.54637 218.401 8.08263 218.898 8.22713C219.394 8.37166 220.001 8.1815 220.514 8.32603C220.908 8.44011 221.129 8.96487 221.46 9.03331C222.012 9.14739 223.557 8.38687 224.298 8.2956C226.45 8.037 228.389 8.66826 229.106 10.7445C229.493 11.8701 228.783 12.5317 229.406 13.1858C231.519 12.0602 234.561 12.8435 236.706 11.718C237.06 11.2921 237.123 5.36756 236.84 4.97208C236.28 4.20395 233.702 5.36756 232.417 4.5614C232.047 4.32564 230.927 2.74375 231.534 2.3863C231.96 2.13533 235.342 2.08209 236.217 1.9528C237.66 1.73986 242.705 0.416542 243.603 0.667515C244.155 0.827225 245.527 2.44714 245.109 3.08598L239.654 4.71351C239.528 4.90364 239.819 5.07095 239.819 5.13179L239.819 11.1476L239.835 11.1324ZM218.732 9.62652C216.628 9.53526 216.123 10.6456 215.91 12.4557L219.3 14.2733L218.732 9.62652ZM203.329 10.1741L200.893 10.9803L200.475 12.9044C202.485 12.912 203.218 12.045 203.329 10.1741ZM222.477 14.5091C223.675 15.224 226.978 12.8207 225.882 11.3833C224.889 10.0828 223.438 11.7104 222.169 11.5506L222.477 14.5091ZM198.197 11.8168C196.534 11.7028 196.218 12.3264 196.1 13.7866C196.069 14.1364 195.816 14.2201 196.352 14.5471C197.756 14.2581 198.378 13.1249 198.197 11.8168Z" fill="#FFFFFF" />
      <path d="M188.217 10.7178L189.463 11.7826C190.425 13.4481 186.349 14.2771 186.522 15.1973C186.664 15.973 190.164 17.2964 190.945 17.5397C191.757 18.6729 189.305 19.9658 188.154 19.6844C187.397 19.5019 185.001 17.0454 184.804 17.2811C185.364 19.6236 182.463 23.1828 180.855 20.1027C180.807 19.5171 183.495 17.7603 182.092 16.7412C180.295 18.7794 177.812 21.4716 176.243 17.5625L180.807 15.5091C179.317 14.49 177.481 15.0756 177.946 12.6496C177.41 12.6343 176.393 12.5659 176.101 12.0944C174.091 8.88497 184.181 10.4821 183.945 7.71377C181.887 8.25374 179.538 9.12834 179.964 6.08624L185.088 5.24967L185.088 4.16212C184.552 3.62975 177.126 6.80114 179.081 2.64107C181.73 2.51938 184.694 1.13523 187.248 1.68281C189.384 3.43962 187.453 5.85048 186.798 7.84305C188.265 8.65682 189.51 7.7594 189.928 10.0181L188.225 10.7254L188.217 10.7178ZM182.51 16.4598L182.959 11.8054C182.4 11.0448 180.255 12.2845 179.38 12.0868L181.824 13.7219L181.375 15.0908L182.203 15.106L182.51 16.4598Z" fill="#FFFFFF" />
      <path d="M175.1 14.8233C173.736 14.77 172.199 16.466 170.685 16.1998C170.031 16.0858 169.408 15.0514 169.377 14.4202C169.322 13.1729 172.018 11.2412 172.254 10.0396C172.412 9.24103 170.67 9.9179 169.96 9.22582C168.525 7.81885 171.631 5.69698 172.372 4.68548C173.232 3.51428 173.91 -0.0373739 175.872 1.50649C178.442 3.52949 172.908 6.86059 173.673 7.71237C178.056 8.60219 173.949 11.188 173.105 13.1805L174.832 13.3022C175.179 13.538 175.155 14.4278 175.1 14.8233C175.297 14.8233 175.486 14.8765 175.667 14.9678C177.386 16.3519 173.594 21.9798 170.819 20.7021C170.236 20.436 169.732 19.2343 170.102 18.6563C170.614 17.8654 174.879 16.2607 175.1 14.8233Z" fill="#FFFFFF" />
      <path d="M235.232 10.6834L233.419 11.5504C232.166 11.558 229.217 6.45488 232.575 6.33319C234.42 6.26475 235.24 9.2384 235.232 10.691L235.232 10.6834Z" fill="#FFFFFF" />
      <path d="M25.6758 20.1498C25.2816 21.4884 22.9561 20.6974 22.2939 19.5642C20.607 16.697 23.571 14.598 21.2612 12.6358C20.7804 12.6358 19.3299 16.7959 19.188 17.4804C18.8727 19.0471 19.1486 21.8686 18.7781 23.1007C18.4628 24.1578 16.8467 24.3403 15.9008 24.0589C13.9536 23.4885 15.3884 19.009 14.4266 18.9254C11.3365 20.34 7.89157 20.3324 4.71472 19.1992C1.21465 17.9595 1.31713 16.2559 0.000653101 13.2899C-0.0308791 12.902 1.08852 10.7497 1.38019 10.2478C3.0041 7.47947 9.02674 0.862904 12.2509 0.17083C14.8523 -0.391958 16.9571 1.57019 17.1226 4.02669C17.2488 6.00405 16.5945 6.27784 14.5922 6.03447C13.7723 1.25838 7.55263 7.64678 6.16521 9.3732C5.06946 10.7421 2.539 13.982 4.47035 15.3205C4.84085 15.5791 7.93099 16.6286 8.43551 16.7123C9.9254 16.9708 15.5776 16.2027 15.1203 13.9972L7.99405 14.9554C6.64607 13.7082 6.63819 11.2593 8.55375 10.5672C9.98059 10.0501 16.9965 8.90928 18.3997 9.06138C20.6385 9.30475 19.9132 10.8638 21.1036 12.0731C21.2928 11.4038 21.1036 10.4912 21.545 9.89796C22.1205 9.12222 23.2557 9.44925 23.6104 7.77609C23.9888 5.98884 23.303 6.06489 22.9482 4.98495C21.3322 -0.00409097 28.8605 -1.41867 31.9507 2.37635C34.8201 5.90518 33.6376 9.28954 31.4146 12.7347L35.9395 16.1875C36.4913 14.7501 36.0578 13.2215 36.2942 11.7384C36.444 10.7878 37.658 8.88646 37.6501 8.38451C37.6344 7.76849 36.4519 7.27413 36.2785 6.41473C36.0735 5.44126 36.5229 4.03429 37.3979 3.49432C38.8641 2.58929 43.9566 1.51696 45.8643 1.22796C46.8339 1.07585 48.5288 0.786855 49.3328 1.30401C50.0817 1.78314 51.1538 4.16358 50.6414 4.98495C50.0817 5.87476 43.3181 6.14855 41.7809 7.42623C41.2448 7.87496 41.1029 8.81041 41.836 9.03857C43.9408 9.70783 47.4882 6.54402 48.5918 9.94359C50.0659 14.4839 43.1446 12.4533 40.425 13.7842C38.8878 14.5372 40.7245 15.7464 41.8676 15.8681C43.523 16.043 45.549 13.8679 46.3688 16.7275C47.6616 21.2602 42.9633 21.0549 39.8653 19.9597C37.9024 19.26 37.0668 18.2029 36.0893 16.4689C34.2525 24.3707 29.6882 16.6894 26.2512 14.6893L25.6836 14.9707L25.6836 20.165L25.6758 20.1498ZM26.8188 9.62417C29.3571 9.31996 31.391 5.04579 27.8436 4.71876L27.4337 5.15987L26.8109 9.62417L26.8188 9.62417Z" fill="#FFFFFF" />
      <path d="M100.383 9.34266C101.148 10.3161 106.343 8.02695 107.651 8.2399C108.866 8.44524 109.819 11.8752 107.778 12.727C106.035 13.4571 102.378 13.4115 100.32 13.7917C99.3427 13.9743 97.435 14.3089 98.3731 15.5181C99.863 17.4346 103.544 14.712 105.034 15.1302C107.344 17.1304 106.004 20.2562 103.032 20.7657C97.1749 21.7696 93.6433 18.7503 94.4237 13.0616C94.6839 11.1831 96.1107 9.57842 96.3945 7.70753C95.8427 7.97372 95.7402 8.70382 95.2278 9.18295C93.3989 10.8789 90.6793 11.609 88.3853 12.5293L87.4236 20.2942C81.4797 22.3172 84.5226 15.9288 83.7973 13.6396C83.5214 12.765 81.9054 12.978 81.8266 10.8561C81.7162 7.60108 84.0575 8.43003 84.3807 7.9433L84.6802 5.69217C82.8198 4.23957 81.0619 7.07632 78.7128 5.79864C78.6812 8.03456 78.7522 10.2705 78.7206 12.5064C78.7128 13.2213 78.4132 13.9058 78.3974 14.6815C78.3659 16.6285 79.8716 21.5719 75.7251 20.8342C73.9041 20.5072 73.8332 17.4194 73.1079 15.9516C72.7926 15.3204 70.7509 12.3163 70.2543 12.1262C70.0651 12.0501 69.5921 11.9665 69.5606 12.2479C68.859 13.7993 69.3162 18.796 68.0391 19.7694C66.3522 21.0471 64.3735 18.2028 64.4681 16.3699C64.6415 13.2365 65.9817 9.71532 66.1314 6.1789C66.1787 5.03051 65.3037 1.22028 67.2114 0.657493C71.421 -0.582162 70.3173 3.83649 70.8849 5.77582C71.1135 6.56677 72.564 8.33877 73.171 9.04606C73.5572 9.49477 74.0775 10.4682 74.7082 10.4454L75.2915 1.83631C76.0798 -0.141057 78.1373 1.15944 78.8547 2.51318C80.2342 1.42563 81.4797 1.22028 83.1746 0.893259C85.3424 0.474968 90.1905 0.292442 92.4687 0.665099C95.9924 1.23549 96.6783 4.49054 96.8123 7.44137L99.5319 2.38389C100.446 2.05686 101.526 2.1101 102.441 1.88955C104.459 1.41042 107.525 0.330468 109.386 0.0642842C110.765 -0.133452 111.002 0.094705 111.617 1.17465C112.192 2.18615 112.775 4.1483 111.672 4.99248C110.363 5.98117 104.041 6.24735 101.81 7.28927C100.762 7.77598 100.801 8.39961 100.391 9.34266L100.383 9.34266ZM87.8414 4.4297C87.7152 7.62388 88.2119 8.55932 91.1759 6.67324C94.1399 4.78714 89.4416 4.23196 87.8414 4.4297Z" fill="#FFFFFF" />
      <path d="M122.63 19.0544C122.243 21.2295 118.956 20.6743 118.649 19.0544C118.46 18.0733 119.169 14.8867 117.829 14.6966C117.119 14.5977 114.281 15.0008 113.635 15.3583C111.719 16.4306 112.003 21.7999 108.842 19.4271C106.083 17.3584 109.221 14.94 109.891 12.8561C110.127 12.126 109.954 11.3123 110.23 10.7191C110.655 9.79882 114.534 4.17095 115.377 3.0758C116.039 2.2164 117.561 0.193408 118.72 0.368328C121.865 1.85135 122.212 5.82129 122.638 8.80253C123.308 8.93942 123.095 8.71887 123.276 8.45269C124.356 6.848 124.403 6.26239 126.248 5.02274C130.481 2.18598 139.137 -0.316144 138.333 7.58571C137.844 12.3846 131.916 10.4148 133.445 6.36887C131.687 5.62355 128.936 7.73781 127.927 9.09153C127.675 9.43376 126.713 11.1602 126.666 11.4644C126.429 13.0158 131.356 15.0008 132.759 15.0997C135.203 15.2746 135.913 13.5102 138.025 14.2707C137.931 11.4263 139.633 9.06111 140.461 6.61223C140.674 5.9886 140.351 5.03795 140.942 4.07208C141.833 2.61188 144.418 2.73356 145.837 2.47498C149.645 1.7829 153.46 1.16688 157.284 0.573669C159.026 0.748589 160.058 3.55493 159.656 5.06837L159.231 5.47905C157.733 5.63116 156.204 5.41061 154.714 5.51708C152.42 5.68439 147.335 5.95058 145.443 6.74152C144.6 7.09136 143.764 8.41466 144.576 8.94703C146.192 10.0042 151.371 6.20916 152.238 10.643C152.885 13.9513 149.006 13.1451 147.012 13.3277C145.538 13.4569 144.087 13.8068 142.59 13.7231C142.519 15.3506 144.844 15.4571 146.121 15.3354C147.808 15.1757 150.693 13.4265 150.883 16.0427C150.985 17.5562 150.528 18.9479 148.88 19.351C146.145 20.0127 143.37 20.469 140.784 19.1457C139.712 18.5981 139.373 17.8604 138.459 17.2824C136.953 20.2864 135.361 20.7884 132.026 20.1648C128.006 19.4118 125.168 16.8869 122.63 13.9969C122.44 15.5408 122.89 17.5942 122.63 19.0544ZM118.641 5.79847C117.253 5.21287 115.842 9.66953 116.079 9.89769C116.82 9.88247 118.113 10.1334 118.507 9.35011L118.649 5.79847L118.641 5.79847Z" fill="#FFFFFF" />
      <path d="M64.9651 5.19066C63.2939 5.91316 58.0595 5.52529 56.8771 6.32384C56.546 6.552 55.3005 8.51415 55.6158 8.80312C58.2882 9.42675 61.3153 5.65458 62.679 9.00086C64.5473 13.5868 58.2251 12.6514 55.3084 13.3054C50.547 14.3778 54.5043 16.2867 57.7285 15.3588C58.8242 15.047 59.4076 13.8834 60.3929 15.5718C62.4189 19.0474 59.3997 20.7205 56.0415 20.4924C45.3126 19.7623 50.8229 9.3507 52.5887 3.31977C53.1642 2.62009 57.5156 1.66943 58.6665 1.49451C62.1351 0.954538 66.3368 0.437383 64.9572 5.20587L64.9651 5.19066Z" fill="#FFFFFF" />
    </svg>
  )
}

export { LogoIcon }
