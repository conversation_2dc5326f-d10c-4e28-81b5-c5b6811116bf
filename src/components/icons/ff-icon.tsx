import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const FFWhiteIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 23.33642578125 25.93505859375"
      fill="none"
      className={className}
      {...props}
    >
      <ellipse cx="11.669097900390625" cy="5.08086633682251" rx="10.451446533203125" ry="4.12432336807251" fill="#001E3D" />
      <path d="M11.6711 0C18.1402 0 23.3364 2.36118 23.3364 5.37516C23.3364 6.18926 22.9558 6.95843 22.2739 7.64289L22.2739 10.6532C22.9117 11.4064 23.3364 12.0475 23.3364 12.9095C23.3364 13.7716 22.9117 14.526 22.2739 15.1715L22.2739 18.2904C22.9117 19.0436 23.3364 19.7922 23.3364 20.5467C23.3364 23.5607 18.1402 25.9276 11.6711 25.9276C5.20193 25.9276 0 23.5638 0 20.5524C0 19.6903 0.424726 18.9373 1.06256 18.2904L1.06256 15.1715C0.424726 14.4171 0 13.7703 0 12.9095C0 12.05 0.421606 11.2988 1.05685 10.6532L1.05685 7.64289C0.374935 6.95843 0 6.19367 0 5.38087C0 2.36689 5.20193 0 11.6711 0ZM11.6711 1.65653C5.51826 1.65653 2.18097 3.6158 2.11942 5.38087C2.05786 7.14594 6.83508 8.60825 11.6711 8.60825C16.5071 8.60825 21.217 7.14976 21.217 5.38087C21.217 3.61198 17.8226 1.65653 11.6711 1.65653ZM7.42652 4.30698C8.59719 4.30698 9.54594 4.78715 9.54594 5.38087C9.54594 5.97459 8.59719 6.45476 7.42652 6.45476C6.25456 6.45476 5.3014 5.97459 5.3014 5.38087C5.3014 4.78715 6.25456 4.30698 7.42652 4.30698Z" fill="#F0F0F0" />
      <path d="M13.8736 14.1561C13.8736 14.1561 13.9889 15.8094 12.8099 16.3474C12.8099 16.3474 13.664 13.2995 9.99601 11.5317C9.99601 11.5317 10.6458 13.1864 8.87466 15.5286C8.87466 15.5286 6.07128 19.229 10.4938 21.2295C10.4938 21.2295 9.45106 18.1595 11.2746 16.6535C11.2746 16.6535 10.8501 18.5335 12.0606 19.4387C12.0783 19.4153 12.0965 19.3922 12.1151 19.3694C12.1337 19.3466 12.1527 19.324 12.1721 19.3017C12.1916 19.2794 12.2114 19.2574 12.2315 19.2356C12.2518 19.2139 12.2723 19.1924 12.2933 19.1713C12.3143 19.1501 12.3356 19.1292 12.3574 19.1086C12.3791 19.0881 12.4012 19.0678 12.4236 19.0478C12.4461 19.0279 12.4689 19.0082 12.492 18.9889C12.5152 18.9696 12.5387 18.9506 12.5625 18.9319C12.5863 18.9132 12.6105 18.8949 12.635 18.8769C12.6595 18.8589 12.6843 18.8412 12.7094 18.8239C12.7346 18.8066 12.76 18.7897 12.7857 18.7731C12.8114 18.7565 12.8375 18.7402 12.8638 18.7244C12.8901 18.7085 12.9167 18.693 12.9436 18.6779C12.9705 18.6627 12.9976 18.648 13.025 18.6336C13.0524 18.6192 13.0801 18.6052 13.108 18.5916C13.1359 18.578 13.164 18.5648 13.1924 18.552C13.1944 18.6074 13.1957 18.6628 13.1963 18.7182C13.1968 18.7736 13.1966 18.829 13.1957 18.8844C13.1947 18.9399 13.193 18.9952 13.1906 19.0506C13.1881 19.106 13.1849 19.1613 13.181 19.2167C13.177 19.272 13.1724 19.3272 13.1669 19.3824C13.1615 19.4376 13.1553 19.4928 13.1484 19.5479C13.1415 19.6029 13.1338 19.6579 13.1254 19.7129C13.117 19.7678 13.1079 19.8226 13.098 19.8773C13.0881 19.9321 13.0775 19.9867 13.0661 20.0412C13.0547 20.0957 13.0427 20.1501 13.0298 20.2043C13.017 20.2586 13.0034 20.3127 12.9892 20.3667C12.9749 20.4206 12.9599 20.4744 12.9441 20.5281C12.9284 20.5817 12.9119 20.6352 12.8947 20.6885C12.8775 20.7418 12.8596 20.795 12.841 20.8479C12.8224 20.9008 12.8031 20.9536 12.783 21.0061C12.763 21.0586 12.7423 21.1109 12.7208 21.163C12.7208 21.163 17.7407 18.6513 13.8736 14.1561Z" fill="#001E3D" />
    </svg>
  )
}

const FFBlackIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 28.82733154296875 32.03076171875"
      fill="none"
      className={className}
      {...props}
    >
      <ellipse cx="13.767791748046875" cy="6.27783203125" rx="12.910614013671875" ry="5.09423828125" fill="#F0F0F0" />
      <path d="M14.4172 0C22.4085 0 28.8274 2.91675 28.8274 6.6399C28.8274 7.64555 28.3571 8.59571 27.5148 9.44122L27.5148 13.1599C28.3027 14.0902 28.8274 14.8822 28.8274 15.9471C28.8274 17.012 28.3027 17.9438 27.5148 18.7413L27.5148 22.594C28.3027 23.5244 28.8274 24.4492 28.8274 25.3812C28.8274 29.1044 22.4085 32.0282 14.4172 32.0282C6.42592 32.0282 0 29.1082 0 25.3883C0 24.3234 0.524661 23.3931 1.31258 22.594L1.31258 18.7413C0.524661 17.8093 0 17.0104 0 15.9471C0 14.8854 0.520807 13.9573 1.30552 13.1599L1.30552 9.44122C0.463155 8.59571 0 7.65101 0 6.64696C0 2.92381 6.42592 0 14.4172 0ZM14.4172 2.0463C6.81668 2.0463 2.69414 4.46657 2.6181 6.64696C2.54207 8.82734 8.44333 10.6337 14.4172 10.6337C20.3911 10.6337 26.2093 8.83206 26.2093 6.64696C26.2093 4.46186 22.0161 2.0463 14.4172 2.0463ZM9.17395 5.32039C10.6201 5.32039 11.792 5.91354 11.792 6.64696C11.792 7.38038 10.6201 7.97353 9.17395 7.97353C7.72622 7.97353 6.54879 7.38038 6.54879 6.64696C6.54879 5.91354 7.72622 5.32039 9.17395 5.32039Z" fill="#081D3B" />
      <path d="M16.686 17.489C16.686 17.489 16.8284 19.5312 15.372 20.1958C15.372 20.1958 16.4271 16.4308 11.8961 14.2471C11.8961 14.2471 12.6987 16.291 10.5109 19.1844C10.5109 19.1844 7.04786 23.7554 12.511 26.2267C12.511 26.2267 11.2229 22.4343 13.4755 20.574C13.4755 20.574 12.9511 22.8963 14.4464 24.0144C14.4683 23.9856 14.4908 23.9571 14.5138 23.9289C14.5368 23.9007 14.5602 23.8728 14.5842 23.8453C14.6082 23.8177 14.6327 23.7905 14.6576 23.7637C14.6826 23.7368 14.708 23.7103 14.7339 23.6841C14.7598 23.658 14.7862 23.6322 14.813 23.6068C14.8398 23.5813 14.8671 23.5563 14.8949 23.5316C14.9226 23.507 14.9507 23.4827 14.9794 23.4588C15.008 23.435 15.037 23.4115 15.0664 23.3884C15.0959 23.3654 15.1257 23.3427 15.156 23.3205C15.1862 23.2983 15.2169 23.2764 15.2479 23.2551C15.279 23.2337 15.3104 23.2127 15.3422 23.1922C15.3739 23.1717 15.4061 23.1517 15.4386 23.1321C15.4711 23.1125 15.504 23.0933 15.5372 23.0746C15.5704 23.0559 15.6039 23.0377 15.6377 23.02C15.6716 23.0022 15.7058 22.9849 15.7402 22.9681C15.7747 22.9513 15.8095 22.935 15.8445 22.9192C15.8471 22.9876 15.8486 23.056 15.8493 23.1245C15.85 23.1929 15.8497 23.2614 15.8486 23.3298C15.8474 23.3983 15.8453 23.4667 15.8423 23.5351C15.8393 23.6035 15.8353 23.6719 15.8304 23.7402C15.8256 23.8085 15.8198 23.8768 15.8131 23.945C15.8064 24.0132 15.7987 24.0813 15.7902 24.1493C15.7816 24.2174 15.7722 24.2853 15.7618 24.3532C15.7514 24.421 15.7401 24.4887 15.7279 24.5563C15.7157 24.6239 15.7026 24.6914 15.6885 24.7587C15.6745 24.8261 15.6596 24.8932 15.6437 24.9603C15.6279 25.0273 15.6111 25.0941 15.5935 25.1608C15.5758 25.2275 15.5573 25.2939 15.5378 25.3602C15.5184 25.4265 15.4981 25.4926 15.4768 25.5584C15.4556 25.6243 15.4335 25.6899 15.4105 25.7553C15.3875 25.8206 15.3636 25.8858 15.3389 25.9507C15.3141 26.0155 15.2885 26.0801 15.262 26.1445C15.262 26.1445 21.463 23.0418 16.686 17.489Z" fill="#F0F0F0" />
    </svg>
  )
}

export { FFBlackIcon, FFWhiteIcon }
