import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const SortedIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24.0008544921875 18.39990234375"
      fill="none"
      className={className}
      {...props}
    >
      <path d="M24.0009 10.09L16.9109 10.09L16.9109 0L14.3909 0L14.3909 18.3999L24.0009 10.09Z" fill="currentColor" />
      <rect x="10.9708251953125" y="13.170166015625" width="4.4266357421875" height="1.66650390625" transform="rotate(180 10.9708251953125 13.170166015625)" fill="currentColor" />
      <rect x="10.9708251953125" y="8.520263671875" width="7.2081298828125" height="1.66650390625" transform="rotate(180 10.9708251953125 8.520263671875)" fill="currentColor" />
      <rect x="10.9708251953125" y="3.869873046875" width="10.9708251953125" height="1.6666259765625" transform="rotate(180 10.9708251953125 3.869873046875)" fill="currentColor" />
    </svg>
  )
}

export { SortedIcon }
