import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const DatahubIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 363 78.29931640625"
      fill="none"
      className={className}
      {...props}
    >
      <path fillRule="evenodd" fill="url(#linear_fill_131_285_0)" fillOpacity="1" d="M38.3218 0.003L55.3223 0.003L55.3223 17.3526L38.3218 17.3526L38.3218 0.003ZM198.576 1.7762L215.62 1.7762L215.62 76.0129L198.576 76.0129L198.576 1.7762ZM340.773 20.8284C336.189 20.8284 331.925 22.6267 328.388 25.705L328.388 1.7762L311.343 1.7762L311.343 76.0137L328.388 76.0137L328.388 73.4218C331.925 76.5001 336.189 78.2984 340.773 78.2984C353.045 78.2984 362.998 65.4348 362.998 49.5698C362.998 33.7047 353.045 20.8412 340.773 20.8412L340.773 20.8284ZM33.4188 23.5992L51.4292 23.5992L51.4292 23.612C55.4677 32.5462 56.4084 43.2722 54.0442 53.5887C50.0057 72.3529 28.8723 76.0136 28.8723 76.0136L0.0005 76.0136L0.0005 6.211L32.6788 6.211L32.6788 23.6056L17.0389 23.6056L17.0389 58.6189L28.866 58.6189C28.866 58.6189 37.2379 54.3951 37.2128 39.7459C37.5263 31.9766 35.2311 27.2279 33.7888 25.0392C33.5505 24.6808 33.4188 24.2584 33.4188 23.8296L33.4188 23.5992ZM131.194 8.6815L114.156 8.6815L114.156 22.4603L105.709 22.4603L105.709 36.6743L114.156 36.6743L114.156 60.9488C114.576 66.3759 117.203 70.0814 119.969 72.5133C122.979 75.1629 126.848 76.5644 130.818 76.5644L142.645 76.5644L142.645 63.3104L135.527 63.3104C132.235 63.3104 131.194 60.9552 131.194 60.9552L131.194 36.6807L142.639 36.6807L142.639 22.4667L131.194 22.4667L131.194 8.6815ZM243.314 27.2477C240.285 23.5742 235.651 20.4319 230.22 20.6111C224.796 20.7967 218.788 24.2654 215.621 28.0029L215.621 42.5176C215.621 42.5176 218.713 36.7642 223.541 36.7642C225.993 36.7642 227.68 38.2489 228.746 39.7081C229.725 41.0521 230.226 42.6968 230.226 44.3736L230.226 76.0014L247.271 76.0014L247.271 38.4665C247.271 36.9434 247.058 35.4266 246.631 33.9675C246.048 31.9835 245.013 29.2892 243.32 27.2349L243.314 27.2477ZM75.2131 22.4602C73.4321 22.9146 71.6386 22.9594 65.4052 26.2232C65.4052 26.2232 62.0063 27.9256 62.207 29.9735C62.3952 31.9574 64.8032 36.1301 65.8881 37.9284C66.973 39.7268 68.0641 39.938 70.9864 38.0052C73.9087 36.0725 77.9723 34.0821 82.707 34.7925C87.4416 35.5029 89.5361 37.8324 87.6109 39.7844C86.4947 41.9027 72.2971 41.3331 64.7781 48.5841C57.2592 55.835 59.1342 64.4875 63.7497 71.3545C67.8133 77.4023 81.0702 79.4246 91.7937 70.6121L91.7937 76.0071L105.715 76.0071L105.715 41.1091C105.715 41.1091 105.464 33.9862 101.677 28.124C97.262 21.289 83.8734 20.2586 75.2194 22.4602L75.2131 22.4602ZM160.255 22.4602C158.474 22.9146 156.68 22.9594 150.447 26.2232C150.447 26.2232 147.054 27.9256 147.249 29.9735C147.437 31.9574 149.845 36.1301 150.93 37.9284C152.014 39.7268 153.106 39.938 156.028 38.0052C158.95 36.0725 163.014 34.0821 167.748 34.7925C172.483 35.5029 174.578 37.8324 172.652 39.7844C171.536 41.9027 157.339 41.3331 149.82 48.5841C142.301 55.835 144.176 64.4875 148.791 71.3545C152.855 77.4023 166.112 79.4246 176.835 70.6121L176.835 76.0071L190.757 76.0071L190.757 41.1091C190.757 41.1091 190.506 33.9862 186.718 28.124C182.303 21.289 168.915 20.2586 160.261 22.4602L160.255 22.4602ZM285.888 56.3729C285.888 56.3729 282.796 62.1262 277.968 62.1262C275.516 62.1262 273.829 60.6415 272.763 59.1823C271.785 57.8384 271.289 56.1937 271.289 54.5169L271.289 22.8893L254.245 22.8893L254.245 60.4239C254.245 61.947 254.458 63.4638 254.884 64.9229C255.467 66.9069 256.502 69.6012 258.195 71.6555C261.224 75.3289 265.858 78.4712 271.289 78.292C276.714 78.1064 282.721 74.6378 285.888 70.9003L285.888 76.0201L302.933 76.0201L302.933 22.8829L285.888 22.8829L285.888 56.3729ZM334.903 64.3277C332.445 64.3277 330.181 63.1629 328.388 61.2238L328.388 38.191C330.181 36.2454 332.445 35.0871 334.903 35.0871C340.748 35.0871 345.483 41.634 345.483 49.7106C345.483 57.7871 340.748 64.3341 334.903 64.3341L334.903 64.3277ZM88.6143 50.5936L88.6143 55.2014C88.6134 58.1172 88.0359 60.7449 86.0306 62.4652C83.8671 64.3211 79.8662 65.3643 76.7997 62.8108C72.7172 59.4957 74.5358 51.7456 88.6143 50.5936ZM173.649 50.5936L173.649 55.2014C173.649 58.1172 173.071 60.7449 171.066 62.4652C168.902 64.3211 164.901 65.3643 161.835 62.8108C157.752 59.4957 159.571 51.7456 173.649 50.5936Z" />
      <path fillRule="evenodd" fill="#FFFFFF" d="M38.3218 0.003L55.3223 0.003L55.3223 17.3526L38.3218 17.3526L38.3218 0.003ZM198.576 1.7762L215.62 1.7762L215.62 76.0129L198.576 76.0129L198.576 1.7762ZM340.773 20.8284C336.189 20.8284 331.925 22.6267 328.388 25.705L328.388 1.7762L311.343 1.7762L311.343 76.0137L328.388 76.0137L328.388 73.4218C331.925 76.5001 336.189 78.2984 340.773 78.2984C353.045 78.2984 362.998 65.4348 362.998 49.5698C362.998 33.7047 353.045 20.8412 340.773 20.8412L340.773 20.8284ZM33.4188 23.5992L51.4292 23.5992L51.4292 23.612C55.4677 32.5462 56.4084 43.2722 54.0442 53.5887C50.0057 72.3529 28.8723 76.0136 28.8723 76.0136L0.0005 76.0136L0.0005 6.211L32.6788 6.211L32.6788 23.6056L17.0389 23.6056L17.0389 58.6189L28.866 58.6189C28.866 58.6189 37.2379 54.3951 37.2128 39.7459C37.5263 31.9766 35.2311 27.2279 33.7888 25.0392C33.5505 24.6808 33.4188 24.2584 33.4188 23.8296L33.4188 23.5992ZM131.194 8.6815L114.156 8.6815L114.156 22.4603L105.709 22.4603L105.709 36.6743L114.156 36.6743L114.156 60.9488C114.576 66.3759 117.203 70.0814 119.969 72.5133C122.979 75.1629 126.848 76.5644 130.818 76.5644L142.645 76.5644L142.645 63.3104L135.527 63.3104C132.235 63.3104 131.194 60.9552 131.194 60.9552L131.194 36.6807L142.639 36.6807L142.639 22.4667L131.194 22.4667L131.194 8.6815ZM243.314 27.2477C240.285 23.5742 235.651 20.4319 230.22 20.6111C224.796 20.7967 218.788 24.2654 215.621 28.0029L215.621 42.5176C215.621 42.5176 218.713 36.7642 223.541 36.7642C225.993 36.7642 227.68 38.2489 228.746 39.7081C229.725 41.0521 230.226 42.6968 230.226 44.3736L230.226 76.0014L247.271 76.0014L247.271 38.4665C247.271 36.9434 247.058 35.4266 246.631 33.9675C246.048 31.9835 245.013 29.2892 243.32 27.2349L243.314 27.2477ZM75.2131 22.4602C73.4321 22.9146 71.6386 22.9594 65.4052 26.2232C65.4052 26.2232 62.0063 27.9256 62.207 29.9735C62.3952 31.9574 64.8032 36.1301 65.8881 37.9284C66.973 39.7268 68.0641 39.938 70.9864 38.0052C73.9087 36.0725 77.9723 34.0821 82.707 34.7925C87.4416 35.5029 89.5361 37.8324 87.6109 39.7844C86.4947 41.9027 72.2971 41.3331 64.7781 48.5841C57.2592 55.835 59.1342 64.4875 63.7497 71.3545C67.8133 77.4023 81.0702 79.4246 91.7937 70.6121L91.7937 76.0071L105.715 76.0071L105.715 41.1091C105.715 41.1091 105.464 33.9862 101.677 28.124C97.262 21.289 83.8734 20.2586 75.2194 22.4602L75.2131 22.4602ZM160.255 22.4602C158.474 22.9146 156.68 22.9594 150.447 26.2232C150.447 26.2232 147.054 27.9256 147.249 29.9735C147.437 31.9574 149.845 36.1301 150.93 37.9284C152.014 39.7268 153.106 39.938 156.028 38.0052C158.95 36.0725 163.014 34.0821 167.748 34.7925C172.483 35.5029 174.578 37.8324 172.652 39.7844C171.536 41.9027 157.339 41.3331 149.82 48.5841C142.301 55.835 144.176 64.4875 148.791 71.3545C152.855 77.4023 166.112 79.4246 176.835 70.6121L176.835 76.0071L190.757 76.0071L190.757 41.1091C190.757 41.1091 190.506 33.9862 186.718 28.124C182.303 21.289 168.915 20.2586 160.261 22.4602L160.255 22.4602ZM285.888 56.3729C285.888 56.3729 282.796 62.1262 277.968 62.1262C275.516 62.1262 273.829 60.6415 272.763 59.1823C271.785 57.8384 271.289 56.1937 271.289 54.5169L271.289 22.8893L254.245 22.8893L254.245 60.4239C254.245 61.947 254.458 63.4638 254.884 64.9229C255.467 66.9069 256.502 69.6012 258.195 71.6555C261.224 75.3289 265.858 78.4712 271.289 78.292C276.714 78.1064 282.721 74.6378 285.888 70.9003L285.888 76.0201L302.933 76.0201L302.933 22.8829L285.888 22.8829L285.888 56.3729ZM334.903 64.3277C332.445 64.3277 330.181 63.1629 328.388 61.2238L328.388 38.191C330.181 36.2454 332.445 35.0871 334.903 35.0871C340.748 35.0871 345.483 41.634 345.483 49.7106C345.483 57.7871 340.748 64.3341 334.903 64.3341L334.903 64.3277ZM88.6143 50.5936L88.6143 55.2014C88.6134 58.1172 88.0359 60.7449 86.0306 62.4652C83.8671 64.3211 79.8662 65.3643 76.7997 62.8108C72.7172 59.4957 74.5358 51.7456 88.6143 50.5936ZM173.649 50.5936L173.649 55.2014C173.649 58.1172 173.071 60.7449 171.066 62.4652C168.902 64.3211 164.901 65.3643 161.835 62.8108C157.752 59.4957 159.571 51.7456 173.649 50.5936Z" />
      <rect x="38.72998046875" y="0" width="17.03515625" height="17.311187744140625" fill="#66CC00" />
      <defs>
        <linearGradient id="linear_fill_131_285_0" x1="-81.7403564453125" y1="-40.5787353515625" x2="259.625" y2="78.2965087890625" gradientUnits="userSpaceOnUse">
          <stop offset="0" stopColor="#BDFFC4" />
          <stop offset="1" stopColor="#5AFF86" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export { DatahubIcon }
