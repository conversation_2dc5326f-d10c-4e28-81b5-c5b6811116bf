import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const SearchIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 23.287498474121094 11.882080078125"
      fill="none"
      className={className}
      {...props}
    >
      <path fillRule="evenodd" fill="currentColor" d="M0.569093 0.035089L11.3221 4.59115C11.5252 4.67718 11.7544 4.67721 11.9575 4.59123L22.7185 0.0348306C23.1305 -0.13964 23.4817 0.381541 23.1652 0.697896L12.2158 11.6436C11.8977 11.9617 11.382 11.9616 11.0639 11.6434L0.122176 0.698019C-0.194164 0.381574 0.157101 -0.139472 0.569093 0.035089Z" />
    </svg>
  )
}

export { SearchIcon }
