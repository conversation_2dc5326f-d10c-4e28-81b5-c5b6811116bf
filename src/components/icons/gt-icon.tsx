import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const GtWhiteIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20.85565185546875 24.33544921875"
      fill="none"
      className={className}
      {...props}
    >
      <ellipse transform="matrix(0.999998927116394, 0, 0, 1.0003125667572021, 12.886474609375, 0.0625)" cx="3.984527587890625" cy="3.9632670879364014" rx="3.984527587890625" ry="3.9632670879364014" fill="#F0F0F0" />
      <ellipse transform="matrix(0.999998152256012, 0, 0, 1.0003433227539062, 10.215087890625, 1.59033203125)" cx="2.22344970703125" cy="2.433385133743286" rx="2.22344970703125" ry="2.433385133743286" fill="#F0F0F0" />
      <ellipse transform="matrix(1.0000051259994507, 0, 0, 1.000317096710205, 8.195556640625, 2.158203125)" cx="1.701141357421875" cy="1.8618247509002686" rx="1.701141357421875" ry="1.8618247509002686" fill="#F0F0F0" />
      <path d="M6.47705 4.67293C5.05887 4.67293 8.4158 2.58203 9.55537 2.58203C9.88798 4.25805 9.31199 5.38096 9.0294 5.68248C8.6892 5.38027 7.89524 4.67293 6.47705 4.67293Z" fill="#F0F0F0" />
      <path d="M4.55994 4.93896L2.96994 4.93896C2.36829 4.93896 1.85888 5.40037 1.79994 5.99931L0.129938 23.0449C0.0622144 23.7331 0.598623 24.3353 1.28994 24.3353L18.9699 24.3353C19.616 24.3353 20.1399 23.8111 20.1399 23.1649L20.1399 12.2013C20.1399 11.4978 19.5279 10.9541 18.8299 11.0409L5.72994 12.6715L5.72994 6.10935C5.72994 5.46311 5.20596 4.93896 4.55994 4.93896ZM15.1308 14.9838L16.998 14.9838C17.3151 14.9838 17.5721 15.241 17.5721 15.5582L17.5721 18.4902C17.5721 18.8073 17.3151 19.0645 16.998 19.0645L15.1308 19.0645C14.8137 19.0645 14.5566 18.8073 14.5566 18.4902L14.5566 15.5582C14.5566 15.241 14.8137 14.9838 15.1308 14.9838ZM9.13987 14.9862L11.0071 14.9862C11.3242 14.9862 11.5812 15.2434 11.5812 15.5606L11.5812 18.4926C11.5812 18.8097 11.3242 19.0669 11.0071 19.0669L9.13987 19.0669C8.82279 19.0669 8.56574 18.8097 8.56574 18.4926L8.56574 15.5606C8.56574 15.2434 8.82279 14.9862 9.13987 14.9862Z" fillRule="evenodd" fill="#F0F0F0" />
    </svg>
  )
}

const GtBlackIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 25.76287841796875 30.06103515625"
      fill="none"
      className={className}
      {...props}
    >
      <ellipse cx="20.84063720703125" cy="4.9739990234375" rx="4.92205810546875" ry="4.8973388671875" fill="#999999" />
      <ellipse cx="15.3653564453125" cy="4.9708251953125" rx="2.74658203125" ry="3.0069580078125" fill="#999999" />
      <ellipse cx="12.2254638671875" cy="4.9666748046875" rx="2.1014404296875" ry="2.3006591796875" fill="#999999" />
      <path d="M8.00106 5.77232C6.24918 5.77232 10.396 3.18945 11.8037 3.18945C12.2146 5.25982 11.503 6.64695 11.154 7.01942C10.7337 6.6461 9.75294 5.77232 8.00106 5.77232Z" fill="#999999" />
      <path d="M5.64002 6.10089L3.67002 6.10089C2.92656 6.10089 2.30283 6.661 2.23002 7.40088L0.160024 28.4709C0.0763674 29.321 0.735794 30.0609 1.59002 30.0609L23.44 30.0609C24.2383 30.0609 24.88 29.4091 24.88 28.6109L24.88 15.0809C24.88 14.2118 24.1224 13.5336 23.26 13.6409L7.08002 15.6509L7.08002 7.55088C7.08002 6.7526 6.4383 6.10089 5.64002 6.10089ZM18.5559 18.5096L21.1328 18.5096C21.4499 18.5096 21.7069 18.7666 21.7069 19.0837L21.7069 22.9762C21.7069 23.2933 21.4499 23.5503 21.1328 23.5503L18.5559 23.5503C18.2389 23.5503 17.9818 23.2933 17.9818 22.9762L17.9818 19.0837C17.9818 18.7666 18.2389 18.5096 18.5559 18.5096ZM11.1553 18.5125L13.7322 18.5125C14.0493 18.5125 14.3063 18.7695 14.3063 19.0866L14.3063 22.9791C14.3063 23.2962 14.0493 23.5532 13.7322 23.5532L11.1553 23.5532C10.8383 23.5532 10.5812 23.2962 10.5812 22.9791L10.5812 19.0866C10.5812 18.7695 10.8383 18.5125 11.1553 18.5125Z" fillRule="evenodd" fill="#999999" />
    </svg>
  )
}

export { GtBlackIcon, GtWhiteIcon }
