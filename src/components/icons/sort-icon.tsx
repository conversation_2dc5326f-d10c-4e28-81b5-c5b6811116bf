import * as React from 'react'

interface CustomSvgProps extends React.SVGProps<SVGSVGElement> {
  className?: string
}

const SortIcon: React.FC<CustomSvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 23.004974365234375 18.40087890625"
      fill="none"
      className={className}
      {...props}
    >
      <path d="M0 8.31432L7.08924 8.31431L7.08924 18.4009L9.60553 18.4009L9.60553 5.53131e-05L0 8.31432ZM23.005 10.0866L15.9157 10.0866L15.9157 5.53131e-05L13.3916 5.53131e-05L13.3916 18.4009L23.005 10.0866Z" fill="currentColor" />
    </svg>
  )
}

export { SortIcon }
