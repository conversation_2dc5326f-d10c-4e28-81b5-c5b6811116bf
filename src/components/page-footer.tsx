import { FooterEmailIcon } from './icons/footer-email-icon'
import { FooterLinkIcon } from './icons/footer-link-icon'
import { FooterLogoIcon } from './icons/footer-logo-icon'

function FooterContent({ title, icon, children, className }: { title: string, icon: React.ReactNode, children: React.ReactNode, className?: string }) {
  return (
    <div className={className}>
      <h3 className="font-bold mb-4 flex items-center gap-[23px] text-[24px] text-[#f7f7f7]">
        {icon}
        {title}
      </h3>
      <div className="flex gap-7 items-center text-[20px] font-light">
        {children}
      </div>
    </div>
  )
}

export function PageFooter() {
  return (
    <footer
      className="text-white mt-40"
      style={{ background: 'linear-gradient(168.7deg, rgba(0, 27, 56, 1) 0%, rgba(1, 56, 100, 1) 100%)' }}
    >
      <div className="container ">

        {/* Logo Section */}
        <div className="flex-shrink-0">
          <FooterLogoIcon className="h-[107px]" />
        </div>
        <div className="mb-16 mt-32 mx-5 flex flex-col md:flex-row justify-between items-center">
          {/* Links Section */}
          <FooterContent className="flex-1" title="友情链接" icon={<FooterLinkIcon className="size-8" />}>
            <a
              href="https://www.greenpeace.org.cn"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[var(--color-text-gray)] hover:text-green-400 transition-colors"
            >
              绿色和平网
            </a>
            <span className="h-[14px] w-[1px] bg-[#969696]" />
            <a
              href="#"
              className="text-[var(--color-text-gray)] hover:text-green-400 transition-colors"
            >
              绿色经济追踪平台
            </a>
          </FooterContent>

          <span className="h-[54px] w-[2px] bg-[#295275] mx-64" />

          {/* Contact Section */}
          <FooterContent className="flex-1" title="服务邮箱" icon={<FooterEmailIcon className="size-8" />}>
            <a
              href="mailto:<EMAIL>"
              className="text-[var(--color-text-gray)] hover:text-green-400 transition-colors"
            >
              <EMAIL>
            </a>
          </FooterContent>
        </div>
      </div>
    </footer>
  )
}
