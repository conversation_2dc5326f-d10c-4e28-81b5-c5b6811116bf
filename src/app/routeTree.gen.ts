/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DetailImport } from './routes/detail'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const DetailRoute = DetailImport.update({
  id: '/detail',
  path: '/detail',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/detail': {
      id: '/detail'
      path: '/detail'
      fullPath: '/detail'
      preLoaderRoute: typeof DetailImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/detail': typeof DetailRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/detail': typeof DetailRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/detail': typeof DetailRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/detail'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/detail'
  id: '__root__' | '/' | '/detail'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DetailRoute: typeof DetailRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DetailRoute: DetailRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/detail"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/detail": {
      "filePath": "detail.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
