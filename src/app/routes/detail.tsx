import { createFileRoute } from '@tanstack/react-router'

import { fundIdAtom } from '@/features/home/<USER>'
import { FundDetail } from '@/features/home/<USER>'
import { setAtom } from '@/helpers/jotai'
import { decodeParam } from '@/helpers/lib/encoding'

export const Route = createFileRoute('/detail')({
  beforeLoad: async ({ search }: { search: { id?: string } }) => {
    if (search.id) {
      const decodedId = decodeParam(search.id)
      setAtom(fundIdAtom, decodedId)
    }
  },

  component: FundDetail,
})
