import { InterfaceType } from './interface-type'

export type 元组转联合<T> = T extends any[] ? T[number] : never

export type Get_API接口路径们<A = InterfaceType> = A extends []
  ? []
  : A extends [infer x, ...infer xs]
  ? 'method' extends keyof x
  ? x['method'] extends 'get'
  ? 'path' extends keyof x
  ? [x['path'], ...Get_API接口路径们<xs>]
  : never
  : never
  : never
  : never
export type Post_API接口路径们<A = InterfaceType> = A extends []
  ? []
  : A extends [infer x, ...infer xs]
  ? 'method' extends keyof x
  ? x['method'] extends 'post'
  ? 'path' extends keyof x
  ? [x['path'], ...Post_API接口路径们<xs>]
  : never
  : never
  : never
  : never


export type 获得对象属性<A, p extends string> = A extends infer X ? (p extends keyof X ? X[p] : never) : never
export type 从路径获得API接口一般属性<Path, A = InterfaceType, Result = never> = A extends [infer First, ...infer Rest]
  ? First extends { path: infer P }
  ? P extends Path
  ? 从路径获得API接口一般属性<Path, Rest, First>
  : 从路径获得API接口一般属性<Path, Rest, Result>
  : 从路径获得API接口一般属性<Path, Rest, Result>
  : Result
export type 从路径计算成功data<路径 extends string> = 获得对象属性<
  获得对象属性<从路径获得API接口一般属性<路径>, 'successOutput'>,
  'data'
>

export type Get请求后端函数类型 = <路径 extends 元组转联合<Get_API接口路径们>>(
  路径: 路径,
  参数: 路径 extends 元组转联合<Get_API接口路径们> ? 获得对象属性<从路径获得API接口一般属性<路径>, 'input'> : never,
) => 路径 extends 元组转联合<Get_API接口路径们>
  ? Promise<获得对象属性<获得对象属性<从路径获得API接口一般属性<路径>, 'successOutput'>, 'data'>>
  : never
export type Post请求后端函数类型 = <路径 extends 元组转联合<Post_API接口路径们>>(
  路径: 路径,
  参数: 路径 extends 元组转联合<Post_API接口路径们> ? 获得对象属性<从路径获得API接口一般属性<路径>, 'input'> : never,
  ws信息回调?: (信息: 获得对象属性<从路径获得API接口一般属性<路径>, 'webSocketData'>) => void,
  ws关闭回调?: (信息: CloseEvent) => void,
  ws错误回调?: (信息: Event) => void,
  获得ws句柄?: (ws句柄: WebSocket) => void,
) => 路径 extends 元组转联合<Post_API接口路径们>
  ? Promise<获得对象属性<获得对象属性<从路径获得API接口一般属性<路径>, 'successOutput'>, 'data'>>
  : never


