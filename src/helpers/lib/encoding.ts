/**
 * Simple encoding and decoding functions for URL parameters
 */

/**
 * Encodes a string to hide the actual value in URLs
 * @param value The string to encode
 * @returns The encoded string
 */
export function encodeParam(value: string): string {
  if (!value)
    return value
  // Simple encoding: Base64 + reverse
  return btoa(value).split('').reverse().join('')
}

/**
 * Decodes a previously encoded string
 * @param encoded The encoded string
 * @returns The original value
 */
export function decodeParam(encoded: string): string {
  if (!encoded)
    return encoded
  // Reverse the string and decode from Base64
  try {
    return atob(encoded.split('').reverse().join(''))
  }
  catch {
    console.error('Failed to decode parameter:', encoded)
    return encoded // Return original if decoding fails
  }
}
