import { baseApi } from './base'
import type { GET_API_INPUTS, GET_API_OUTPUTS, GET_API_PATHS, POST_API_INPUTS, POST_API_OUTPUTS, POST_API_PATHS } from './types'

type ApiClient = {
  get: <P extends GET_API_PATHS>(path: P, params?: GET_API_INPUTS<P>) => Promise<GET_API_OUTPUTS<P>>
  post: <P extends POST_API_PATHS>(path: P, body?: POST_API_INPUTS<P>, wsId?: string) => Promise<POST_API_OUTPUTS<P>>
}

function createApiClient(): ApiClient {
  return {
    get: async <P extends GET_API_PATHS>(path: P, params?: GET_API_INPUTS<P>) =>
      await baseApi(path, { params }) as Promise<GET_API_OUTPUTS<P>>,
    post: async <P extends POST_API_PATHS>(path: P, body?: POST_API_INPUTS<P>, wsId?: string) =>
      await baseApi(path, {
        method: 'POST',
        body,
        headers: wsId ? { 'ws-client-id': wsId } : undefined,
      }) as Promise<POST_API_OUTPUTS<P>>,
  }
}

export const api = createApiClient()
