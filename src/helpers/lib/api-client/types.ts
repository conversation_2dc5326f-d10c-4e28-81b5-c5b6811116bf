import type { Get_API接口路径们, Post_API接口路径们, 从路径获得API接口一般属性 } from '@/helpers/types/api'

export type GET_API_PATHS = Get_API接口路径们[number]
export type POST_API_PATHS = Post_API接口路径们[number]
export type ALL_API_PATHS = GET_API_PATHS | POST_API_PATHS

export type GET_API_INPUTS<P extends GET_API_PATHS> = 从路径获得API接口一般属性<P>['input']
export type GET_API_OUTPUTS<P extends GET_API_PATHS> = 从路径获得API接口一般属性<P>['successOutput'] | 从路径获得API接口一般属性<P>['errorOutput']
export type GET_API_DATA<P extends GET_API_PATHS> = 从路径获得API接口一般属性<P>['successOutput']
export type GET_API_ERROR<P extends GET_API_PATHS> = 从路径获得API接口一般属性<P>['errorOutput']

export type POST_API_INPUTS<P extends POST_API_PATHS> = 从路径获得API接口一般属性<P>['input']
export type POST_API_OUTPUTS<P extends POST_API_PATHS> = 从路径获得API接口一般属性<P>['successOutput'] | 从路径获得API接口一般属性<P>['errorOutput']
export type POST_API_DATA<P extends POST_API_PATHS> = 从路径获得API接口一般属性<P>['successOutput']
export type POST_API_ERROR<P extends POST_API_PATHS> = 从路径获得API接口一般属性<P>['errorOutput']
export type POST_API_WS_DATA<P extends POST_API_PATHS> = 从路径获得API接口一般属性<P>['webSocketData']
