import { useState } from 'react'

export function useDownload<T, R = Buffer>(fn: (query: T) => Promise<R>, fileName: string) {
  const [isPending, setIsPending] = useState(false)

  async function download(query: T) {
    setIsPending(true)
    const data = await fn(query)
    setIsPending(false)
    if (data instanceof Blob) {
      const res = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const url = URL.createObjectURL(res)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.append(a)
      a.click()
      a.remove()
      URL.revokeObjectURL(url)
    }
  }

  return {
    isPending,
    setIsPending,
    download,
  }
}
