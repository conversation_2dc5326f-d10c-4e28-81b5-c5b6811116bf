import ReactECharts from 'echarts-for-react'
import { useAtom, useAtomValue } from 'jotai'
import { useMemo } from 'react'

import lineSymbol from '@/assets/line-symbol.svg'
import lineSymbolGray from '@/assets/line-symbol-gray.svg'

import { currentStockAtom, getCarbonInvestmentQueryAtom } from '../atoms'

const option = {
  title: {
    left: 'center',
    top: 10,
    textStyle: {
      fontSize: 32,
      fontWeight: 'bold',
      fontFamily: '"Source Han Sans SC", sans-serif',
      lineHeight: 40,
    },
  },
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: [{
      name: '化石燃料相关',
      icon: 'rect',
      textStyle: {
        fontSize: '16px',
      },
    }, {
      name: '高碳',
      icon: 'rect',
      textStyle: {
        fontSize: '16px',
      },

    }],
    top: 100,
    right: 10,
  },
  grid: {
    top: 200,
    left: 60,
    right: 80,
    bottom: '18%',
    containLabel: true,
  },
  dataZoom: [
    {
      bottom: 80,
      type: 'slider',
      xAxisIndex: 0,
      start: 0,
      end: 40,
      brushSelect: false,
      height: 8,
      backgroundColor: '#f5f5f5',
      fillerColor: '#222',
      borderColor: 'rgba(0,0,0,0)',
      // zoomLock: true,
      handleIcon:
        'path://M512 512m-32 0a32 32 0 1 0 64 0a32 32 0 1 0 -64 0',
      handleSize: 20,
      handleStyle: {
        color: '#fff',
        borderColor: '#222',
        borderWidth: 6,
        shadowBlur: 0,
      },
      textStyle: {
        fontSize: 20,
      },
    },
  ],
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLabel: {
      color: '#3d3d3d',
      fontSize: 22,
      fontWeight: 500,
    },
  },
  yAxis: {
    type: 'value',
    name: '投\n资\n市\n值\n比\n%',
    nameLocation: 'middle',
    nameGap: 40,
    nameRotate: 0,
    nameTextStyle: {
      fontSize: 20,
      color: 'rgba(153, 153, 153, 1)',
      lineHeight: 27.84,
    },
    axisLabel: {
      color: '#999',
      fontSize: 20,
      fontWeight: 500,
    },

  },
}

const markPoint = {
  symbol: `image://${lineSymbol}`,
  symbolSize: [73, 34],
  symbolOffset: [0, -30],
  label: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    align: 'center',
    offset: [0, -2],
  },
}

export function DetailLine() {
  const { data: list } = useAtomValue(getCarbonInvestmentQueryAtom)
  const [currentStock] = useAtom(currentStockAtom)

  const echartData = useMemo(() => {
    return {
      ffProportionInYearList: list?.ffProportionInYearList?.map(item => (item.toFixed(2))),
      gtProportionInYearList: list?.gtProportionInYearList?.map(item => (item.toFixed(2))),
    }
  }, [list])

  const years = useMemo(() => {
    return list?.allYears
  }, [list])

  const echartOption = useMemo(() => {
    return {
      ...option,
      title: {
        ...option.title,
        text: `${currentStock}\n中化石燃料相关行业以及高碳类型行业的投资公司投资占比`,
      },
      xAxis: {
        ...option.xAxis,
        data: years || [],
      },
      graphic: [
        {
          type: 'text',
          left: 30,
          bottom: 40,
          style: {
            text: years?.[0] || '',
            fill: 'rgba(179, 179, 179, 1)',
            fontSize: 22,
          },
        },
        {
          type: 'text',
          right: 60,
          bottom: 40,
          style: {
            text: years?.[years.length - 1] || '',
            fill: 'rgba(179, 179, 179, 1)',
            fontSize: 22,
          },
        },
      ],
      series: [
        {
          name: '化石燃料相关',
          type: 'line',
          data: echartData.ffProportionInYearList,
          lineStyle: {
            color: 'rgba(8, 29, 59, 1)', // 设置线条颜色
          },
          itemStyle: {
            color: 'rgba(8, 29, 59, 1)', // 设置拐点颜色
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(132, 156, 181, 1)',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 255, 255, 1)',
                },
              ],
            },
          },
          markPoint: {
            ...markPoint,
            data: years?.map((year, index) => ({
              name: year,
              coord: [year, echartData.ffProportionInYearList?.[index]],
              value: echartData.ffProportionInYearList?.[index],
            })),
          },
        },
        {
          name: '高碳',
          type: 'line',
          data: echartData.gtProportionInYearList,
          lineStyle: {
            color: 'rgba(153, 153, 154, 1)', // 设置线条颜色
          },
          itemStyle: {
            color: 'rgba(153, 153, 154, 1)', // 设置拐点颜色
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(229, 230, 229, 1)',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 254, 255, 1)',
                },
              ],
            },
          },
          markPoint: {
            ...markPoint,
            symbol: `image://${lineSymbolGray}`,
            data: years?.map((year, index) => ({
              name: year,
              coord: [year, echartData.gtProportionInYearList?.[index]],
              value: echartData.gtProportionInYearList?.[index],
            })),
          },
        },
      ],

    }
  }, [years, echartData, currentStock])

  return (
    <div className="pt-4 bg-white container">
      <ReactECharts option={echartOption} style={{ height: 1068 }} />
    </div>
  )
}
