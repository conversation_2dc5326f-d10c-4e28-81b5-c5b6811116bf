import { useAtomValue } from 'jotai'
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react'
import { useMemo, useState } from 'react'

import { HomeSearch } from '@/components/home-search'
import { Button } from '@/components/ui/button'
import { cn } from '@/helpers/utils'

import { getFundOptionsQueryAtom } from '../atoms'

export function HomeBanner() {
  const { data } = useAtomValue(getFundOptionsQueryAtom)

  const [more, setMore] = useState(false)
  const fundOptions = useMemo(() => {
    if (!data) {
      return {
        fundNames: [],
        companies: [],
        fundTypes: [],
      }
    }
    const companies = data.companies.map(name => ({
      label: name,
      value: name,
    }))
    return {
      fundNames: data.fundNames,
      companies,
      fundTypes: data.fundTypes,
    }
  }, [data])

  return (
    <div className="container z-10">
      <div className="mt-[118px] mb-48">
        <span className="text-7xl font-bold text-white">可持续主题基金</span>
        <div className="mt-[48px] max-w-[1215px]">
          <div
            className={cn(
              'overflow-hidden transition-all duration-200 ease-in-out',
              more ? 'max-h-[1000px]' : 'max-h-[118px]',
            )}
          >
            <div className="text-[22px] font-normal leading-[38px] tracking-[0px] text-white text-left align-top text-shadow-[0px_2px_4px_rgba(0,0,0,0.7)]">
              <div>气候变化已经成为威胁全球生态平衡和社会经济稳定的重大风险之一。</div>
              <div>
                作为金融体系的重要组成部分，资产管理机构在引导资源配置、推动可持续发展方面具备独特优势。凭借广泛的资本运作能力和投资决策影响力，该行业能够为实体经济中的低碳项日提供关键支持，助力绿色转型进程，同时实现风险管理与长期收益的平衡。
                <div>在国际层面，ESG 投资与绿色金融持续升温，监管标准不断提升，各类"洗绿"行为受到更多审视。</div>
                中国在"双碳"战略和政策引导下，责任投资市场不断扩容，绿色资金供给日益增强，投资者对资管公司的"绿色"程度关注度更喜。因此，本数据库评估对比了国内头部资产管理公司发行的几十支可持续主题基金产品针对高碳排放行业 / 公司的投资情况，从而为关注可持续投融资的相关投资者、学者以及其他利益相关方提供数据参考，帮助甄别投资产品的"洗绿"风险。
              </div>
            </div>
            <div className="text-[22px] font-medium leading-[38px] tracking-[0px] text-white text-left align-top">本数据库覆盖自 2023 年起的相关数据，并将持续更新。</div>

          </div>
          <Button
            className="mt-4 text-base rounded-[3px] border-[0.6px] border-[#A6A6A6] text-[#A6A6A6] bg-transparent hover:bg-transparent transition-all duration-200 hover:border-white hover:text-white"
            onClick={() => setMore(!more)}
          >
            {
              more ? (
                <>
                  收起更多
                  <ChevronUpIcon className="ml-1 transition-transform duration-200" />
                </>
              ) : (
                <>
                  展开全部
                  <ChevronDownIcon className="ml-1 transition-transform duration-200" />
                </>
              )
            }
          </Button>
          {/* 下拉搜索框 */}
          <HomeSearch
            options={fundOptions.fundNames}
            placeholder="请输入您想查询的基金名称"
          />
        </div>
      </div>
    </div>
  )
}
