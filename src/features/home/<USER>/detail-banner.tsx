import { Link } from '@tanstack/react-router'
import { useAtom, useAtomValue } from 'jotai'
import { ArrowLeftIcon } from 'lucide-react'
import { useCallback, useEffect, useMemo } from 'react'

import { SelectIcon } from '@/components/icons/select-icon'
import { SelectWithIcon } from '@/components/select-with-icon'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'

import { currentStockAtom, fundIdAtom, getFundNamesQueryAtom } from './../atoms'

export function DetailBanner() {
  const id = useAtomValue(fundIdAtom)
  const { data, isLoading } = useAtomValue(getFundNamesQueryAtom)
  const [, setFundIdAtom] = useAtom(fundIdAtom)
  const [, setCurrentStockAtom] = useAtom(currentStockAtom)

  const defaultValue = useMemo(() => {
    return id || (data?.fundNames?.[0]?.value || '')
  }, [id, data])

  const dataList = useMemo(() => {
    return data?.fundNames?.map(item => ({ label: item.label, value: item.value })) || []
  }, [data])

  const handleChange = useCallback((value: string) => {
    setFundIdAtom(value)
    const fundName = data?.fundNames?.find(item => item.value === value)?.label || ''
    setCurrentStockAtom(fundName)
  }, [setFundIdAtom, setCurrentStockAtom, data])

  useEffect(() => {
    if (!id) {
      setFundIdAtom(data?.fundNames?.[0]?.value || '')
    }
  }, [data, id, setFundIdAtom])

  useEffect(() => {
    setCurrentStockAtom(data?.fundNames?.find(item => item.value === id)?.label || '')
  }, [data, id, setCurrentStockAtom])

  return (
    <div className="flex justify-between container pt-14 pb-7">
      <Button variant="ghost" asChild className="flex items-center gap-2 cursor-default">
        <Link to="/">
          <ArrowLeftIcon className="!size-5" />
          <span className="text-xl">返回</span>
        </Link>
      </Button>
      <div>
        {isLoading ? (
          <Skeleton className="h-10 w-24" />
        ) : (
          <SelectWithIcon
            value={defaultValue}
            onValueChange={handleChange}
            data={dataList}
            icon={<SelectIcon className="w-4 h-4 mr-3 flex-shrink-0" />}
            placeholder="基金名称"
          />
        )}
      </div>
    </div>
  )
}
