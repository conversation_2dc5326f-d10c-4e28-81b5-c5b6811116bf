import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useAtom, useAtomValue } from 'jotai'
import { CalendarIcon } from 'lucide-react'
import { useMemo } from 'react'
import type { DateRange } from 'react-day-picker'
import type { SubmitHandler } from 'react-hook-form'
import { useForm } from 'react-hook-form'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { MultiSelect } from '@/components/ui/multi-select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/helpers/utils'

import { getFundOptionsQueryAtom, queryFormAtom, syncQueryParamsAtom } from '../atoms'

type FormValues = {
  name: string[]
  company: string[]
  type: string
  date: DateRange
}

export function HomeForm() {
  const { data } = useAtomValue(getFundOptionsQueryAtom)
  const [, syncQueryParams] = useAtom(syncQueryParamsAtom)
  const [, setFormValues] = useAtom(queryFormAtom)

  const fundOptions = useMemo(() => {
    if (!data) {
      return {
        fundNames: [],
        companies: [],
        fundTypes: [],
      }
    }
    const companies = data.companies.map(name => ({
      label: name,
      value: name,
    }))
    return {
      fundNames: data.fundNames,
      companies,
      fundTypes: data.fundTypes,
    }
  }, [data])

  const form = useForm<FormValues>({
    defaultValues: {
      name: [],
      company: [],
      type: '',
      date: {
        from: undefined,
        to: undefined,
      },
    },
  })

  const handleChange = (field: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const onSubmit: SubmitHandler<FormValues> = () => {
    syncQueryParams()
  }

  const onReset = () => {
    form.reset()
    syncQueryParams()
  }

  return (
    <Form {...form}>
      <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-4">
              <FormLabel className="w-16 text-right">基金名称</FormLabel>
              <FormControl className="flex-1">
                <MultiSelect
                  options={fundOptions.fundNames}
                  onValueChange={(val) => {
                    field.onChange(val)
                    handleChange('fundNames', val)
                  }}
                  placeholder="请输入基金名称"
                  variant="inverted"
                  animation={2}
                  maxCount={3}
                  value={field.value}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="company"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-4">
              <FormLabel className="w-16 text-right">基金公司</FormLabel>
              <FormControl className="flex-1">
                <MultiSelect
                  options={fundOptions.companies}
                  onValueChange={(val) => {
                    field.onChange(val)
                    handleChange('companies', val)
                  }}
                  placeholder="请输入基金公司名称"
                  variant="inverted"
                  animation={2}
                  maxCount={3}
                  value={field.value}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-4">
              <FormLabel className="w-16 text-right">基金类型</FormLabel>
              <FormControl className="flex-1">
                <Select
                  onValueChange={(val) => {
                    field.onChange(val)
                    handleChange('fundType', val)
                  }}
                  value={field.value}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="请选择基金类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {fundOptions.fundTypes.map(name => (
                        <SelectItem key={name} value={name}>
                          {name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-4">
              <FormLabel className="w-16 text-right">发行日期</FormLabel>
              <FormControl className="flex-1">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-[240px] justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {field.value?.from ? (
                        field.value.to
                          ? (
                              <>
                                {format(field.value.from, 'yyyy年MM月dd日', { locale: zhCN })}
                                {' '}
                                -
                                {' '}
                                {format(field.value.to, 'yyyy年MM月dd日', { locale: zhCN })}
                              </>
                            )
                          : (
                              format(field.value.from, 'yyyy年MM月dd日', { locale: zhCN })
                            )
                      ) : (
                        <span>请选择发行日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={field.value}
                      onSelect={(val) => {
                        field.onChange(val)
                        handleChange('issueDateStart', val?.from?.toISOString() || undefined)
                        handleChange('issueDateEnd', val?.to?.toISOString() || undefined)
                      }}
                      initialFocus
                      locale={zhCN}
                    />
                  </PopoverContent>
                </Popover>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-4">
          <Button onClick={onReset} variant="outline">
            重置
          </Button>
          <Button type="submit">查询</Button>
        </div>
      </form>
    </Form>
  )
}
