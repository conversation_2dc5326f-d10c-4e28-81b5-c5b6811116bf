import { useNavigate } from '@tanstack/react-router'
import type {
  Cell,
  Column,
  ColumnDef,
  Header,
  HeaderGroup,
  Row,
} from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useAtomValue } from 'jotai'
import {
  ArrowDownToLineIcon,
} from 'lucide-react'
import { useMemo, useState } from 'react'

import { ResetIcon } from '@/components/icons/reset-icon'
import { SearchIcon } from '@/components/icons/search-icon'
import { SortIcon } from '@/components/icons/sort-icon'
import { SortedIcon } from '@/components/icons/sorted-icon'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import type {
  POST_API_INPUTS,
  POST_API_OUTPUTS,
} from '@/helpers/lib/api-client/types'
import { encodeParam } from '@/helpers/lib/encoding'
import { cn } from '@/helpers/utils'

import type { FundInfo } from '../atoms'
import {
  exportHomeList,
  getFundListQueryAtom,
  getFundOptionsQueryAtom,
} from '../atoms'
import { useDownload } from '../hooks/useDownload'

type SortDirection = 'asc' | 'desc' | false

function SearchButton({
  column,
  list,
  hasSelection,
  currentFilterValue,
}: {
  column: Column<FundInfo, unknown>
  list: string[]
  hasSelection: boolean
  currentFilterValue: string[] | undefined
}) {
  const [isOpen, setIsOpen] = useState(false)

  const handleValueChange = (value: string) => {
    const currentValues = currentFilterValue || []

    // 单个选项的切换
    if (currentValues.includes(value)) {
      // 如果已选中，则取消选中
      const newValues = currentValues.filter(v => v !== value)
      column.setFilterValue(newValues.length > 0 ? newValues : undefined)
    }
    else {
      // 如果未选中，则添加到选中列表
      column.setFilterValue([...currentValues, value])
    }
  }

  // 判断某个选项是否被选中
  const isSelected = (value: string) => {
    return currentFilterValue?.includes(value) || false
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="!h-[23px] w-full border-none p-0 hover:bg-transparent focus:ring-0 focus-visible:ring-0 group"
          onClick={() => setIsOpen(!isOpen)}
        >
          <SearchIcon
            className={`h-[12px] w-[23px] transition-all duration-200 group-hover:!text-[var(--color-green)] ${isOpen ? 'rotate-180' : 'rotate-0'
              }`}
            style={{
              color: isOpen
                ? 'var(--color-green)'
                : hasSelection
                  ? 'var(--color-green)'
                  : '#777777',
            }}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[534px] max-h-[360px] overflow-y-auto rounded-none p-0"
        align="center"
      >
        <div className="flex flex-col py-1.5">
          {list.map(item => (
            <div key={item} className="px-2.5 py-1 text-[20px]">
              <button
                type="button"
                className={`px-3 py-2 w-full hover:bg-[#081d3b] hover:text-white active:bg-[#081d3b] active:text-white text-left font-medium ${isSelected(item) ? 'bg-[#081d3b] text-white' : ''
                  }`}
                onClick={() => handleValueChange(item)}
              >
                {item}
              </button>
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}

function SortButton({
  column,
  sortDirection,
}: {
  column: Column<FundInfo, unknown>
  sortDirection: SortDirection
}) {
  const renderIcon = () => {
    if (!sortDirection) {
      return (
        <SortIcon className="h-[12px] w-[23px] transition-all duration-200 group-hover:!text-[var(--color-green)] !text-[#777777]" />
      )
    }
    else if (!(sortDirection === 'asc')) {
      return (
        <SortedIcon className="h-[12px] w-[23px] transition-all duration-200 group-hover:!text-[var(--color-green)] text-[var(--color-green)]" />
      )
    }
    return (
      <SortedIcon className="h-[12px] w-[23px] transition-all duration-200 group-hover:!text-[var(--color-green)] rotate-180 text-[var(--color-green)]" />
    )
  }

  return (
    <Button
      variant="ghost"
      onClick={() => {
        if (!sortDirection) {
          column.toggleSorting(true)
        }
        else if (sortDirection === 'desc') {
          column.toggleSorting(false)
        }
        else {
          column.clearSorting()
        }
      }}
      className="!h-[23px] w-full border-none p-0 hover:bg-transparent focus:ring-0 focus-visible:ring-0 group"
    >
      {renderIcon()}
    </Button>
  )
}

function CustomTableHeader({
  title,
  children,
  isSelected,
  className,
}: {
  title: string
  children?: React.ReactNode
  isSelected: boolean
  className?: string
}) {
  return (
    <div className={cn('flex flex-col items-center justify-center pt-[15px] px-[8px] pb-[14px]', className)}>
      <div
        className={cn(
          'text-[18px] w-[98px] h-[58px] text-wrap text-center items-center flex justify-center font-normal',
          isSelected && 'font-bold',
        )}
      >
        {title}
      </div>
      <div className="h-[21px]">{children}</div>
    </div>
  )
}

export function HomeTable() {
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} Finance of Future Fund List.xlsx`

  const navigate = useNavigate()

  const { data = [], isLoading } = useAtomValue(getFundListQueryAtom)
  const { data: fundOptions } = useAtomValue(getFundOptionsQueryAtom)
  const { isPending, download } = useDownload<
    POST_API_INPUTS<'/api/funds/export-list'>,
    POST_API_OUTPUTS<'/api/funds/export-list'>
  >(exportHomeList, fileName)

  async function handleDownload() {
    // 获取当前的筛选条件
    const { columnFilters } = table.getState()

    // 获取当前的排序条件
    const { sorting } = table.getState()

    // 构建查询参数
    const queryParams: POST_API_INPUTS<'/api/funds/export-list'> = {}

    // 处理筛选条件
    columnFilters.forEach((filter) => {
      const { id, value } = filter
      if (value && Array.isArray(value) && value.length > 0) {
        // 将筛选条件映射到查询参数
        switch (id) {
          case 'fundName': {
            queryParams.fundNames = value
            break
          }
          case 'fundCompany': {
            queryParams.companies = value
            break
          }
          case 'fundType': {
            // fundType 在 API 中只接受单个值，取第一个值
            queryParams.fundType = value[0]
            break
          }
        }
      }
    })

    // 处理排序条件
    if (sorting.length > 0) {
      const sortField = sorting[0]
      queryParams.sort = {
        name: sortField.id,
        order: sortField.desc ? 'desc' : 'asc',
      }
    }

    await download(queryParams)
  }

  function handleReset() {
    table.resetColumnFilters()
    table.resetSorting()
  }

  const columns: Array<ColumnDef<FundInfo>> = useMemo(() => {
    const { companies = [], fundNames = [], fundTypes = [] } = fundOptions || {}
    const fundNameList = fundNames.map(({ label }) => label)

    return [
      {
        accessorKey: 'windCode',
        header: () => <CustomTableHeader title="WIND代码" isSelected={false} />,
      },
      {
        accessorKey: 'fundName',
        cell: ({ row }) => {
          const { windCode, fundName } = row.original
          return (
            <span
              className="cursor-pointer text-primary underline decoration-blue-600"
              onClick={() => navigate({ to: '/detail', search: { id: encodeParam(windCode) } })}
            >
              {fundName}
            </span>
          )
        },
        header: ({ column }) => {
          // 获取当前的筛选值（数组形式）
          const currentFilterValue = column.getFilterValue() as
            | string[]
            | undefined
          // 判断是否有选中项
          const hasSelection = !!(
            currentFilterValue && currentFilterValue.length > 0
          )
          return (
            <CustomTableHeader title="基金名称" isSelected={hasSelection}>
              <SearchButton
                column={column}
                list={fundNameList}
                hasSelection={hasSelection}
                currentFilterValue={currentFilterValue}
              />
            </CustomTableHeader>
          )
        },
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
      },
      {
        accessorKey: 'fundCompany',
        header: ({ column }) => {
          // 获取当前的筛选值（数组形式）
          const currentFilterValue = column.getFilterValue() as
            | string[]
            | undefined
          // 判断是否有选中项
          const hasSelection = !!(
            currentFilterValue && currentFilterValue.length > 0
          )
          return (
            <CustomTableHeader title="基金公司" isSelected={hasSelection}>
              <SearchButton
                column={column}
                list={companies}
                hasSelection={hasSelection}
                currentFilterValue={currentFilterValue}
              />
            </CustomTableHeader>
          )
        },
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
      },
      {
        accessorKey: 'fundType',
        header: ({ column }) => {
          // 获取当前的筛选值（数组形式）
          const currentFilterValue = column.getFilterValue() as
            | string[]
            | undefined
          // 判断是否有选中项
          const hasSelection = !!(
            currentFilterValue && currentFilterValue.length > 0
          )
          return (
            <CustomTableHeader title="基金类型" isSelected={hasSelection} className="px-4">
              <SearchButton
                column={column}
                list={fundTypes}
                hasSelection={hasSelection}
                currentFilterValue={currentFilterValue}
              />
            </CustomTableHeader>
          )
        },
        filterFn: (row, columnId, filterValue) => {
          if (!filterValue || filterValue.length === 0)
            return true
          const cellValue = row.getValue(columnId) as string
          return filterValue.includes(cellValue)
        },
        cell: ({ row }) => {
          return (
            <div className="px-2">
              {row.original.fundType}
            </div>
          )
        },
      },
      {
        accessorKey: 'issueDate',
        header: ({ column }) => {
          const sortDirection = column.getIsSorted() as SortDirection
          return (
            <CustomTableHeader title="发行日期" isSelected={!!sortDirection}>
              <SortButton column={column} sortDirection={sortDirection} />
            </CustomTableHeader>
          )
        },
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'highCarbonRatio',
        header: ({ column }) => {
          const sortDirection = column.getIsSorted() as SortDirection
          return (
            <CustomTableHeader
              title="高碳相关投资比例"
              isSelected={!!sortDirection}
            >
              <SortButton column={column} sortDirection={sortDirection} />
            </CustomTableHeader>
          )
        },
      },
      {
        accessorKey: 'fossilFuelRatio',
        header: ({ column }) => {
          const sortDirection = column.getIsSorted() as SortDirection
          return (
            <CustomTableHeader
              title="化石燃料相关投资比例"
              isSelected={!!sortDirection}
            >
              <SortButton column={column} sortDirection={sortDirection} />
            </CustomTableHeader>
          )
        },
      },
      {
        accessorKey: 'fundSize',
        header: ({ column }) => {
          const sortDirection = column.getIsSorted() as SortDirection
          return (
            <CustomTableHeader
              title="基金规模（万元）"
              isSelected={!!sortDirection}
            >
              <SortButton column={column} sortDirection={sortDirection} />
            </CustomTableHeader>
          )
        },
      },
      {
        accessorKey: 'fundSizeUpdateDate',
        header: ({ column }) => {
          const sortDirection = column.getIsSorted() as SortDirection
          return (
            <CustomTableHeader
              title="基金规模更新日期"
              isSelected={!!sortDirection}
            >
              <SortButton column={column} sortDirection={sortDirection} />
            </CustomTableHeader>
          )
        },
        sortingFn: 'datetime',
      },
    ]
  }, [fundOptions, navigate])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <>
      <div className="border-b-[2px] border-[rgba(61, 61, 61, 1)]">
        <div className="">
          <div className="overflow-auto">
            <div className="min-w-[1042px]">
              <Table className="w-full table-fixed border-collapse">
                <TableHeader className="bg-[var(--color-blue)] shadow">
                  {table
                    .getHeaderGroups()
                    .map((headerGroup: HeaderGroup<FundInfo>) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map(
                          (header: Header<FundInfo, unknown>) => {
                            return (
                              <TableHead
                                key={header.id}
                                className="bg-[var(--color-blue)] text-white"
                              >
                                {header.isPlaceholder
                                  ? null
                                  : flexRender(
                                      header.column.columnDef.header,
                                      header.getContext(),
                                    )}
                              </TableHead>
                            )
                          },
                        )}
                      </TableRow>
                    ))}
                </TableHeader>
              </Table>
            </div>
            <div className="overflow-auto max-h-[1080px] min-w-[1042px]">
              <Table className="w-full table-fixed border-collapse">
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center"
                      >
                        Loading...
                      </TableCell>
                    </TableRow>
                  ) : table.getRowModel().rows?.length
                    ? (
                        table
                          .getRowModel()
                          .rows
                          .map((row: Row<FundInfo>, index: number) => (
                            <TableRow
                              key={row.id}
                              className={`border-none ${index % 2 === 0
                              ? 'bg-[rgba(249, 249, 249, 1)]'
                              : 'bg-[rgba(245,245,245,1)]'
                              }`}
                            >
                              {row
                                .getVisibleCells()
                                .map((cell: Cell<FundInfo, unknown>) => (
                                  <TableCell
                                    key={cell.id}
                                    className="text-center whitespace-normal text-[20px]"
                                  >
                                    {flexRender(
                                      cell.column.columnDef.cell,
                                      cell.getContext(),
                                    )}
                                  </TableCell>
                                ))}
                            </TableRow>
                          ))
                      )
                    : (
                        <TableRow>
                          <TableCell
                            colSpan={columns.length}
                            className="h-24 text-center text-xl"
                          >
                            暂无数据
                          </TableCell>
                        </TableRow>
                      )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-11 flex justify-between items-center">
        <Button
          onClick={handleReset}
          variant="ghost"
          className="hover:bg-transparent"
        >
          <ResetIcon className="!size-[34px]" />
          <span className="text-[25px]">重置</span>
        </Button>
        <Button
          onClick={handleDownload}
          disabled={isPending}
          className="bg-[var(--color-blue)] text-white text-[22px] font-normal tracking-normal hover:bg-[var(--color-blue)] disabled:opacity-50 rounded-none h-[58px] !px-10"
        >
          <ArrowDownToLineIcon className="!w-5 !h-5" />
          <span className="">{isPending ? '下载中...' : '下载数据'}</span>
        </Button>
      </div>
    </>
  )
}
