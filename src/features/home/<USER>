import { atom } from 'jotai'
import { atomWithQuery } from 'jotai-tanstack-query'
import { toast } from 'sonner'

import { api } from '@/helpers/lib/api-client'
import type { POST_API_DATA, POST_API_INPUTS, POST_API_OUTPUTS } from '@/helpers/lib/api-client/types'

export type FundInfo = POST_API_DATA<'/api/funds/get-list'>['data'][number]

export type QueryParams = {
  fundNames?: string[]
  companies?: string[]
  fundType?: string
  issueDateStart?: string
  issueDateEnd?: string
}

export const fundIdAtom = atom<string>()
export const selectedYearAtom = atom<string>('')
export const yearListAtom = atom<string[]>([])
export const currentStockAtom = atom<string>('')

export const markAtom = atom<POST_API_INPUTS<'/api/funds/get-stocks-list'>['mark']>('ff')

export const queryFormAtom = atom<QueryParams>({
  fundNames: undefined,
  companies: undefined,
  fundType: undefined,
  issueDateStart: undefined,
  issueDateEnd: undefined,
})

export const queryParamsAtom = atom<QueryParams | null>(null)
export const syncQueryParamsAtom = atom(null, (get, set) => {
  const formValues = get(queryFormAtom)
  set(queryParamsAtom, formValues)
})

export const getFundListQueryAtom = atomWithQuery((get) => {
  const params = get(queryParamsAtom)
  return {
    queryKey: ['fundList', params],
    queryFn: async () => {
      const res = await api.post('/api/funds/get-list')
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export type FundOptions = POST_API_DATA<'/api/funds/filters'>['data']

export const getFundOptionsQueryAtom = atomWithQuery(() => ({
  queryKey: ['fundOptions'],
  queryFn: async () => {
    const res = await api.post('/api/funds/filters')
    if (res.status === 'success') {
      return res.data
    }
    else {
      toast.error(res.data)
    }
  },
}))

export const getFundNamesQueryAtom = atomWithQuery(() => ({
  queryKey: ['fundNames'],
  queryFn: async () => {
    const res = await api.post('/api/funds/get-names')
    if (res.status === 'success') {
      return res.data
    }
    else {
      toast.error(res.data)
    }
  },
}))

export const getCarbonInvestmentQueryAtom = atomWithQuery((get) => {
  const params = get(fundIdAtom)
  return {
    queryKey: ['carbonInvestment', params],
    queryFn: async () => {
      if (!params) {
        return
      }
      const res = await api.post('/api/funds/get-carbon-investment', {
        windId: params,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export const getCompanyTypeStatisticsQueryAtom = atomWithQuery((get) => {
  const params = get(fundIdAtom)
  const year = get(selectedYearAtom)

  return {
    queryKey: ['carbonInvestment', params, year],
    queryFn: async () => {
      if (!year) {
        return
      }
      const res = await api.post('/api/funds/company-type-statistics', {
        windId: params!,
        year,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export const getYearsQueryAtom = atomWithQuery((get) => {
  const windId = get(fundIdAtom)

  return {
    queryKey: ['years', windId],
    queryFn: async () => {
      if (!windId) {
        return
      }
      const res = await api.post('/api/funds/get-years', {
        windId: get(fundIdAtom)!,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },

  }
})

export type TopHoldingsInfo = POST_API_DATA<'/api/funds/get-top-holdings'>['data'][number]

export const getTopHoldingsQueryAtom = atomWithQuery((get) => {
  const windId = get(fundIdAtom)
  const year = get(selectedYearAtom)
  return {
    queryKey: ['top', windId, year],
    queryFn: async () => {
      if (!year) {
        return []
      }
      const res = await api.post('/api/funds/get-top-holdings', {
        windId: windId!,
        year,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export async function exportHomeList(data: POST_API_INPUTS<'/api/funds/export-list'>) {
  return api.post('/api/funds/export-list', data)
}

export type DownloadTopHoldingsParams = POST_API_INPUTS<'/api/funds/export-top-holdings-info'>
export type DownLoadTopHoldingsResult = POST_API_OUTPUTS<'/api/funds/export-top-holdings-info'>

export async function exportTopHoldings(data: DownloadTopHoldingsParams) {
  return api.post('/api/funds/export-top-holdings-info', data)
}

export type DownloadStocksListParams = POST_API_INPUTS<'/api/funds/export-holdings'>
export type DownLoadStocksListResult = POST_API_OUTPUTS<'/api/funds/export-holdings'>

export async function exportHoldings(data: DownloadStocksListParams) {
  return api.post('/api/funds/export-holdings', data)
}
