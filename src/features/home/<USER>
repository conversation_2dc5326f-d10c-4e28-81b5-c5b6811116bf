import { PageFooter } from '@/components/page-footer'
import { PageHeader } from '@/components/page-header'

import { HomeBanner } from './components/home-banner'
import { HomeTable } from './components/home-table'

export function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="relative">
        <div className="absolute top-0 left-0 w-full bg-[url('/src/assets/home-banner.jpg')] bg-cover bg-center bg-no-repeat h-[950px] home-page-bg" />
        <div className="absolute bottom-0 left-0 w-full h-24 bg-white home-page-bg-bottom" style={{ clipPath: 'ellipse(60% 100% at 50% 100%)' }} />
        <PageHeader />
        <div className="container relative px-2 home-page-container">
          <HomeBanner />
        </div>
      </div>
      <div className="p-6 pt-16 bg-white z-10 home-page-content">
        <div className="container">
          <HomeTable />
        </div>
      </div>
      <PageFooter />
    </div>
  )
}
