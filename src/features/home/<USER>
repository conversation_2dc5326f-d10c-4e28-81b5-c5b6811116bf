import { LogoIcon } from '@/components/icons/logo-icon'
import { PageFooter } from '@/components/page-footer'

import { DetailBanner } from './components/detail-banner'
import { DetailBar } from './components/detail-bar'
import { DetailLine } from './components/detail-line'
import { DetailTopHoldings } from './components/detail-top-holdings'

export function FundDetail() {
  return (
    <div className="flex min-h-screen flex-col bg-[#f5f5f5]">
      <header className="flex h-16 relative bg-[var(--color-blue)] fund-detail-header">
        <div className="flex items-center container">
          <div className="flex items-center">
            <LogoIcon className="h-6 fund-detail-logo" />
          </div>
        </div>
      </header>
      <DetailBanner />
      <DetailLine />
      <DetailBar />
      <DetailTopHoldings />
      <PageFooter />
    </div>
  )
}
