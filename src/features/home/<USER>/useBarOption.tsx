import { useMemo } from 'react'

import type { POST_API_DATA } from '@/helpers/lib/api-client/types'

const Option = {
  tooltip: {
    trigger: 'item',
  },
  xAxis: {
    axisLabel: {
      color: 'rgba(61, 61, 61, 1)',
      fontSize: 20,
      fontWeight: 500,
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      color: 'rgba(153, 153, 153, 1)',
      fontSize: 18,
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
      },
    },
    splitArea: {
      show: true,
      areaStyle: {
        color: ['rgba(250, 250, 250, 1)', 'rgba(255, 255, 255, 1)'],
      },
    },
  },
}

export type UseOptionProps = {
  data?: POST_API_DATA<'/api/funds/company-type-statistics'>['data']
}

export function useBarOption({ data }: UseOptionProps) {
  const ffOption = useMemo(() => {
    return {
      ...Option,
      xAxis: {
        ...Option.xAxis,
        type: 'category',
        data: ['非化石燃料', '化石燃料'],
      },
      series: [
        {
          name: '化石燃料相关行业投资公司数量占比',
          type: 'bar',
          data: [
            {
              value: data?.ff?.notIn || 0,
              mark: 'nt_ff',
            },
            {
              value: data?.ff?.in || 0,
              mark: 'ff',
            },
          ],
          label: {
            show: true,
            position: 'top',
            fontSize: 28,
            fontWeight: 500,
            color: 'inherit',
          },
          itemStyle: {
            color: (params: any) => {
              const colorList = ['rgba(91, 163, 245, 1)', 'rgba(8, 29, 59, 1)']
              return colorList[params.dataIndex]
            },
          },

        },
      ],
    }
  }, [data?.ff])

  const gtOption = useMemo(() => {
    return {
      ...Option,
      xAxis: {
        ...Option.xAxis,
        type: 'category',
        data: ['非高碳', '高碳'],
      },
      series: [
        {
          name: '高碳类型行业公司数量占比',
          type: 'bar',
          data: [
            {
              value: data?.gt?.notIn || 0,
              mark: 'nt_gt',
            },
            {
              value: data?.gt?.in || 0,
              mark: 'gt',
            },
          ],
          label: {
            show: true,
            position: 'top',
            fontSize: 28,
            fontWeight: 500,
            color: 'inherit',
          },
          itemStyle: {
            color: (params: any) => {
              const colorList = ['rgba(102, 204, 0, 1)', 'rgba(153, 153, 153, 1)']
              return colorList[params.dataIndex]
            },
          },
        },
      ],
    }
  }, [data?.gt])

  return {
    ffOption,
    gtOption,
  }
}
