{"name": "gp-fff-frontend", "type": "module", "version": "0.0.8", "private": true, "packageManager": "pnpm@10.6.5", "scripts": {"build": "tsc -b && vite build", "dev": "vite", "lint": "eslint .", "preview": "vite preview", "release": "pnpm run build && bumpp"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@tanstack/react-router": "^1.120.5", "@tanstack/react-router-devtools": "^1.120.5", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "jotai": "^2.12.4", "jotai-tanstack-query": "^0.9.0", "lucide-react": "^0.508.0", "next-themes": "^0.4.6", "ofetch": "^1.4.1", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.27.0", "@tanstack/router-plugin": "^1.120.5", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "bumpp": "^10.1.1", "eslint": "^9.27.0", "eslint-config-hyoban": "^4.0.7", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.1.0", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.1", "vite": "^6.3.5"}}