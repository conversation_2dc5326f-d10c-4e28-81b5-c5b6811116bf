// @ts-check
import { defineConfig } from 'eslint-config-hyoban'
import reactCompilerEslintPlugin from 'eslint-plugin-react-hooks'

export default defineConfig({
  react: 'vite',
  tailwindCSS: false,
  ignores: ['src/helpers/types', 'src/app/routeTree.gen.ts', 'src/components/ui'],
}, [{
  plugins: {
    reactCompilerEslintPlugin,
  },
  rules: {
    '@eslint-react/no-unstable-default-props': 'off',
    'tailwindcss/no-custom-classname': 'off',
  },
}])
